# 故障排除指南

## 🚨 当前问题：Swagger启动错误

您遇到的错误是Spring Boot 2.7.x与Swagger版本兼容性问题。我已经为您提供了多种解决方案。

## ✅ 解决方案

### 方案1：使用Maven启动（推荐）

如果您有Maven，这是最简单的方法：

```bash
# 安装Maven（如果没有）
# macOS: brew install maven
# Ubuntu: sudo apt install maven

# 启动应用
mvn spring-boot:run
```

### 方案2：禁用Swagger启动

我已经在配置中禁用了Swagger，现在应用应该能正常启动：

```bash
# 检查配置
grep -A2 "swagger:" src/main/resources/application.yml

# 应该显示：
# swagger:
#   enabled: false
```

### 方案3：使用简化版控制器

我创建了不包含Swagger注解的简化版控制器：
- `SimpleTokenController.java` - 令牌管理
- `SimpleClothingController.java` - AI穿衣功能

## 🔧 当前状态

### ✅ 已完成的修复

1. **SDK集成** - 成功集成本地SDK `sdk-java-5.0.6.jar`
2. **包名更新** - 更新为正确的包名 `com.fzzixun.openapi.sdk.*`
3. **API配置** - 您已配置了正确的API密钥
4. **Swagger禁用** - 默认禁用Swagger避免启动问题
5. **简化控制器** - 创建了不依赖Swagger的控制器

### 📋 可用的API接口

即使没有Swagger文档，您仍然可以使用以下API：

#### 令牌管理
```bash
# 获取应用令牌
curl -X GET "http://localhost:8080/api/token/app"

# 刷新应用令牌
curl -X POST "http://localhost:8080/api/token/app/refresh"

# 健康检查
curl -X GET "http://localhost:8080/api/token/health"
```

#### AI穿衣
```bash
# AI穿衣处理
curl -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://example.com/person.jpg",
    "clothingImage": "https://example.com/clothing.jpg",
    "clothingType": "上衣",
    "style": "casual",
    "quality": "high"
  }'

# 查询任务状态
curl -X GET "http://localhost:8080/api/clothing/task/{taskId}"

# 健康检查
curl -X GET "http://localhost:8080/api/clothing/health"
```

## 🚀 启动应用

### 如果有Maven
```bash
mvn spring-boot:run
```

### 如果没有Maven
由于Spring Boot需要大量依赖，建议安装Maven：

**macOS:**
```bash
brew install maven
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install maven
```

**Windows:**
下载并安装：https://maven.apache.org/download.cgi

## 🔍 验证应用运行

启动成功后，您应该看到：
```
=== 紫鸟AI穿衣Demo应用启动成功 ===
API文档地址: http://localhost:8080/doc.html
Swagger UI: http://localhost:8080/swagger-ui/
```

然后可以测试：
```bash
# 测试健康检查
curl http://localhost:8080/api/token/health
curl http://localhost:8080/api/clothing/health

# 测试获取令牌
curl http://localhost:8080/api/token/app
```

## 📝 启用Swagger（可选）

如果您想启用Swagger文档，修改 `application.yml`：

```yaml
swagger:
  enabled: true  # 改为true
```

然后重启应用，访问：
- Knife4j文档: http://localhost:8080/doc.html
- Swagger UI: http://localhost:8080/swagger-ui/

## 🆘 如果仍有问题

1. **检查Java版本**：确保使用Java 8或更高版本
2. **检查端口**：确保8080端口未被占用
3. **查看日志**：检查 `logs/ziniao-ai-demo.log` 文件
4. **网络连接**：确保能访问 `https://sbappstoreapi.ziniao.com`

## 📞 联系支持

如果问题仍然存在，请提供：
1. Java版本：`java -version`
2. 错误日志：`logs/ziniao-ai-demo.log` 的内容
3. 启动命令和完整错误信息
