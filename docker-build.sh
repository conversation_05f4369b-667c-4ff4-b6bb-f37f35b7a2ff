#!/bin/bash

# 紫鸟AI穿衣演示 Docker构建脚本
# 此脚本构建一个可以离线运行的自包含Docker镜像

set -e  # 遇到错误时退出

echo "=== 紫鸟AI穿衣演示 - Docker构建脚本 ==="
echo ""

# 配置
IMAGE_NAME="ziniao-ai-demo"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

# 输出颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 彩色输出函数
print_status() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查Docker是否已安装并运行
print_status "检查Docker安装状态..."
if ! command -v docker &> /dev/null; then
    print_error "Docker未安装。请先安装Docker。"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker未运行。请先启动Docker。"
    exit 1
fi

print_success "Docker已安装并正在运行"

# 检查必需文件
print_status "检查必需文件..."

required_files=(
    "Dockerfile"
    "pom.xml"
    "sdk-java-5.0.6.jar"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    print_error "缺少必需文件："
    for file in "${missing_files[@]}"; do
        echo "  - $file"
    done
    print_status "请先运行: ./prepare-offline.sh"
    exit 1
fi

print_success "所有必需文件都已找到"

# 检查lib目录是否存在，如果不存在则下载依赖
if [ ! -d "lib" ] || [ ! "$(ls -A lib)" ]; then
    print_warning "在lib/目录中未找到依赖"
    if [ -f "prepare-offline.sh" ]; then
        print_status "正在运行准备脚本..."
        ./prepare-offline.sh
    else
        print_warning "未找到prepare-offline.sh。依赖将在Docker构建过程中下载。"
    fi
fi

# 构建Docker镜像
print_status "正在构建Docker镜像: $FULL_IMAGE_NAME"
echo ""

docker build \
    --tag "$FULL_IMAGE_NAME" \
    --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
    --build-arg VCS_REF="$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" \
    .

if [ $? -eq 0 ]; then
    print_success "Docker镜像构建成功: $FULL_IMAGE_NAME"
    echo ""
    
    # 显示镜像信息
    print_status "镜像信息:"
    docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    echo ""
    print_status "要运行容器，请使用:"
    echo "  ./docker-run.sh"
    echo ""
    print_status "或手动运行:"
    echo "  docker run -d --name ziniao-demo -p 8080:8080 $FULL_IMAGE_NAME"
    echo ""
    print_status "或使用Docker Compose:"
    echo "  cp .env.example .env  # 编辑.env文件配置API凭据"
    echo "  docker-compose up -d"
    
else
    print_error "Docker构建失败"
    exit 1
fi
