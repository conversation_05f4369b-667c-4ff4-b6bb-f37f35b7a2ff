# 宝塔面板静态文件访问配置指南

## 🚨 问题现象
访问图片URL时出现404错误：
```
http://39.108.93.224:18088/uploads/2025/07/25/20250725_025105_1543619a.png
```

## 🔧 解决方案

### 方案一：Nginx配置（推荐）

1. **登录宝塔面板**
2. **进入"网站"管理**
3. **找到你的网站，点击"设置"**
4. **点击"配置文件"**
5. **在server块中添加以下配置**：

```nginx
# 静态文件配置
location /uploads/ {
    alias /www/wwwroot/fs.vwo50.life_998/uploads/;
    expires 1h;
    add_header Cache-Control "public, immutable";
    
    # 允许跨域访问
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
    
    # 处理图片文件
    location ~* \.(jpg|jpeg|png|gif|bmp|webp)$ {
        expires 7d;
        add_header Cache-Control "public, immutable";
    }
}

# 代理Java应用
location / {
    proxy_pass http://127.0.0.1:18088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

6. **保存配置并重载Nginx**

### 方案二：Apache配置

如果你使用Apache，添加以下配置：

```apache
# 静态文件别名
Alias /uploads /www/wwwroot/fs.vwo50.life_998/uploads

<Directory "/www/wwwroot/fs.vwo50.life_998/uploads">
    Options Indexes FollowSymLinks
    AllowOverride None
    Require all granted
    
    # 设置缓存
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType image/jpg "access plus 7 days"
        ExpiresByType image/jpeg "access plus 7 days"
        ExpiresByType image/png "access plus 7 days"
        ExpiresByType image/gif "access plus 7 days"
        ExpiresByType image/webp "access plus 7 days"
    </IfModule>
</Directory>

# 代理Java应用
ProxyPass /api/ http://127.0.0.1:18088/api/
ProxyPassReverse /api/ http://127.0.0.1:18088/api/
```

### 方案三：直接端口访问

如果不想配置反向代理，可以直接通过Java应用端口访问：

1. **确保18088端口在防火墙中开放**
2. **直接访问**：`http://39.108.93.224:18088/uploads/xxx.png`

## 🛠️ 配置步骤详解

### 1. 检查文件权限
```bash
# SSH登录服务器执行
chmod -R 755 /www/wwwroot/fs.vwo50.life_998/uploads/
chown -R www:www /www/wwwroot/fs.vwo50.life_998/uploads/
```

### 2. 创建测试文件
```bash
echo "Test file" > /www/wwwroot/fs.vwo50.life_998/uploads/test.txt
chmod 644 /www/wwwroot/fs.vwo50.life_998/uploads/test.txt
```

### 3. 测试访问
访问：`http://39.108.93.224:18088/uploads/test.txt`

### 4. 检查Java应用配置
确保Java应用启动命令包含正确的配置：
```bash
/www/server/java/jdk1.8.0_371/bin/java -jar -Xmx1024M -Xms256M \
-Dfile.upload.path=/www/wwwroot/fs.vwo50.life_998/uploads/ \
-Dfile.upload.server-base-url=http://39.108.93.224:18088 \
-Dfile.upload.url-prefix=/uploads/ \
/www/wwwroot/fs.vwo50.life_998/jar/ziniao-ai-demo-1.0.0.jar \
--server.port=18088
```

## 🔍 故障排除

### 检查清单：
- [ ] uploads目录存在且有正确权限
- [ ] 图片文件存在且可读
- [ ] Nginx/Apache配置正确
- [ ] 防火墙端口开放
- [ ] Java应用正常运行

### 常见问题：

1. **403 Forbidden**：权限问题
   ```bash
   chmod -R 755 /www/wwwroot/fs.vwo50.life_998/uploads/
   ```

2. **404 Not Found**：路径配置错误
   - 检查Nginx/Apache配置
   - 检查文件实际路径

3. **502 Bad Gateway**：Java应用未运行
   - 检查Java应用状态
   - 检查端口监听

### 测试命令：
```bash
# 检查文件是否存在
ls -la /www/wwwroot/fs.vwo50.life_998/uploads/2025/07/25/20250725_025105_1543619a.png

# 检查端口监听
netstat -tlnp | grep :18088

# 测试HTTP访问
curl -I http://39.108.93.224:18088/uploads/test.txt
```

## ✅ 验证成功

配置成功后，以下URL应该能正常访问：
- `http://39.108.93.224:18088/uploads/test.txt`
- `http://39.108.93.224:18088/uploads/2025/07/25/20250725_025105_1543619a.png`

配置完成后，所有图片URL都应该能够正常显示！
