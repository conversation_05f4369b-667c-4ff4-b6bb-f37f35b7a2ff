#!/bin/bash

# 直接调用飞书API测试脚本
# 用于查看真实的多维表格数据结构

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试数据配置
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  直接调用飞书API测试${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 启动服务器获取token
echo -e "${YELLOW}1. 启动服务器获取访问令牌...${NC}"
nohup java -jar target/ziniao-ai-demo-1.0.0.jar > logs/app.log 2>&1 &
sleep 10

SERVER_URL="http://localhost:8080"

# 检查服务器状态
if curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器启动失败${NC}"
    exit 1
fi

# 获取访问令牌
echo -e "${YELLOW}2. 直接调用飞书API获取访问令牌...${NC}"

# 飞书应用配置
APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
FEISHU_BASE_URL="https://open.feishu.cn"

TOKEN_RESPONSE=$(curl -s -X POST "$FEISHU_BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{\"app_id\": \"$APP_ID\", \"app_secret\": \"$APP_SECRET\"}")

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'code' in data and data['code'] == 0 and 'app_access_token' in data:
        print(data['app_access_token'])
    else:
        print('ERROR: 无法获取访问令牌')
        print('响应:', data)
except Exception as e:
    print('ERROR: 解析令牌响应失败:', e)
" 2>/dev/null)

if [[ "$ACCESS_TOKEN" == "ERROR:"* ]] || [[ -z "$ACCESS_TOKEN" ]]; then
    echo -e "${RED}❌ 获取访问令牌失败${NC}"
    echo "响应: $TOKEN_RESPONSE"
    pkill -f "java.*ziniao"
    exit 1
fi

echo -e "${GREEN}✅ 成功获取访问令牌${NC}"
echo "令牌前缀: ${ACCESS_TOKEN:0:20}..."

echo ""
echo -e "${YELLOW}3. 直接调用飞书多维表格API...${NC}"

# 构建API URL
FEISHU_URL="$FEISHU_BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables/$TABLE_ID/records"
if [ ! -z "$VIEW_ID" ]; then
    FEISHU_URL="${FEISHU_URL}?view_id=$VIEW_ID&page_size=1&user_id_type=user_id"
else
    FEISHU_URL="${FEISHU_URL}?page_size=1&user_id_type=user_id"
fi

echo "请求URL: $FEISHU_URL"

# 调用飞书API
FEISHU_RESPONSE=$(curl -s -X GET "$FEISHU_URL" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json; charset=utf-8")

echo ""
echo -e "${YELLOW}4. 飞书API响应:${NC}"
echo "$FEISHU_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$FEISHU_RESPONSE"

echo ""
echo -e "${YELLOW}5. 分析字段结构...${NC}"

# 分析字段
FIELD_ANALYSIS=$(echo "$FEISHU_RESPONSE" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'code' in data and data['code'] == 0:
        if 'data' in data and 'items' in data['data'] and len(data['data']['items']) > 0:
            record = data['data']['items'][0]
            if 'fields' in record:
                fields = record['fields']
                print('✅ 找到字段数据')
                print(f'记录ID: {record.get(\"record_id\", \"未知\")}')
                print(f'字段数量: {len(fields)}')
                print()
                print('字段详情:')
                for field_name, field_value in fields.items():
                    field_type = type(field_value).__name__
                    print(f'  📝 {field_name}:')
                    print(f'     类型: {field_type}')
                    
                    if isinstance(field_value, list):
                        print(f'     数组长度: {len(field_value)}')
                        if len(field_value) > 0:
                            first_item = field_value[0]
                            first_type = type(first_item).__name__
                            print(f'     第一个元素类型: {first_type}')
                            if isinstance(first_item, dict):
                                print(f'     包含键: {list(first_item.keys())}')
                                # 检查是否是附件字段
                                if 'file_token' in first_item:
                                    print(f'     🖼️  这是一个附件字段!')
                                    print(f'     文件令牌: {first_item.get(\"file_token\", \"无\")}')
                                    print(f'     文件名: {first_item.get(\"name\", \"无\")}')
                    elif isinstance(field_value, str):
                        if len(field_value) > 100:
                            print(f'     值: {field_value[:100]}...')
                        else:
                            print(f'     值: {field_value}')
                        # 检查是否是URL
                        if field_value.startswith('http'):
                            print(f'     🔗 这可能是一个URL字段!')
                    else:
                        print(f'     值: {field_value}')
                    print()
                    
                # 检查特定字段
                target_fields = ['👚上装正面图', '👚上装背面图', '上装正面图', '上装背面图']
                found_fields = []
                for target in target_fields:
                    if target in fields:
                        found_fields.append(target)
                        
                if found_fields:
                    print(f'🎯 找到目标字段: {found_fields}')
                else:
                    print('❌ 没有找到目标字段 (👚上装正面图, 👚上装背面图)')
                    print('可用的字段名称:')
                    for name in fields.keys():
                        print(f'  - {name}')
            else:
                print('❌ 记录中没有fields字段')
        else:
            print('❌ 响应中没有记录数据')
    else:
        error_code = data.get('code', '未知')
        error_msg = data.get('msg', '未知错误')
        print(f'❌ API调用失败: {error_code} - {error_msg}')
except Exception as e:
    print(f'❌ 解析错误: {e}')
" 2>/dev/null)

echo "$FIELD_ANALYSIS"

echo ""
echo -e "${BLUE}========================================${NC}"
echo "测试完成！"

# 停止服务器
pkill -f "java.*ziniao"
