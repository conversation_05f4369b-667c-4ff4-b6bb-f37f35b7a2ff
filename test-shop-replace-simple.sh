#!/bin/bash

# 商品替换接口测试脚本（简化版，不依赖jq）

# 设置服务器地址
SERVER_URL="http://localhost:8080"

# 测试商品替换接口
echo "=== 测试商品替换接口 ==="

# 构建请求数据
REQUEST_DATA='{
  "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/96a7a42fc965491dbc625d2db09f2478.png",
  "sourceImageUrl": "",
  "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/9778678b4724467485346248032387ec.png",
  "targetImageUrl": "",
  "denoiseStrength": "0.5",
  "imageOutputWidth": "",
  "imageOutputHeight": "",
  "outputNum": 1
}'

echo "请求数据:"
echo "$REQUEST_DATA"

echo ""
echo "发送商品替换请求..."

# 发送请求
RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/shopReplace" \
  -H "Content-Type: application/json" \
  -d "$REQUEST_DATA")

echo "响应结果:"
echo "$RESPONSE"

# 简单检查响应
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo ""
    echo "✅ 商品替换请求提交成功!"
    
    # 尝试提取任务ID（简单方式）
    if echo "$RESPONSE" | grep -q '"id"'; then
        echo "响应中包含任务ID"
        
        echo ""
        echo "=== 测试健康检查接口 ==="
        HEALTH_RESPONSE=$(curl -s -X GET "$SERVER_URL/api/clothing/health")
        echo "健康检查响应: $HEALTH_RESPONSE"
    else
        echo "⚠️  响应中未找到任务ID"
    fi
else
    echo ""
    echo "❌ 商品替换请求失败"
    echo "请检查服务是否正常运行和配置是否正确"
fi

echo ""
echo "=== 测试完成 ==="
