# targetRowIndex 功能说明

## 功能概述

在字段映射图片上传功能的基础上，新增了 `targetRowIndex` 字段，允许用户精确控制只处理表格中的第几行数据，而不是处理所有行。

## 功能特性

### 1. 精确行控制
- **targetRowIndex**: 指定要处理的行索引（从1开始）
- **默认行为**: 不填或为null时，处理所有行
- **索引范围**: 1到表格总行数
- **超出范围**: 自动处理为空结果，不报错

### 2. 测试验证结果

| 测试场景 | targetRowIndex | 处理结果 | 说明 |
|---------|---------------|----------|------|
| 测试1 | 不填 | 4条记录，11张图片 | 处理所有行 |
| 测试2 | 1 | 1条记录，3张图片 | 只处理第1行 |
| 测试3 | 2 | 1条记录，3张图片 | 只处理第2行 |
| 测试4 | 999 | 0条记录，0张图片 | 超出范围，空结果 |

## API 使用示例

### 1. 处理所有行（默认行为）
```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "fieldMapping": {
    "upperOriginUrl附件": "upperOriginUrl",
    "downOriginUrl附件": "downOriginUrl",
    "modelImageUrl附件": "modelImageUrl"
  }
}
```

### 2. 只处理第1行数据
```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "targetRowIndex": 1,
  "fieldMapping": {
    "upperOriginUrl附件": "upperOriginUrl",
    "downOriginUrl附件": "downOriginUrl"
  }
}
```

### 3. 只处理第3行数据
```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "targetRowIndex": 3,
  "fieldMapping": {
    "upperOriginUrl附件": "upperOriginUrl"
  }
}
```

## 响应示例

### 处理单行的响应
```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalRecords": 1,
    "totalImages": 3,
    "successfulDownloads": 3,
    "failedDownloads": 0,
    "records": [
      {
        "recordId": "recuRlQ3eEFJEB",
        "imageFields": {
          "upperOriginUrl附件": [
            {
              "downloadStatus": "SUCCESS",
              "localAccessUrl": "http://localhost:8080/api/image-proxy/id/1197c5884005"
            }
          ],
          "downOriginUrl附件": [
            {
              "downloadStatus": "SUCCESS",
              "localAccessUrl": "http://localhost:8080/api/image-proxy/id/12339de16859"
            }
          ],
          "modelImageUrl附件": [
            {
              "downloadStatus": "SUCCESS",
              "localAccessUrl": "http://localhost:8080/api/image-proxy/id/4034ebd3557b"
            }
          ]
        }
      }
    ]
  }
}
```

## 实现细节

### 1. 请求模型更新
在 `FeishuFieldMappingImageRequest` 中新增：
```java
@ApiModelProperty(value = "指定要处理的行索引（从1开始），不填则处理所有行", example = "1")
private Integer targetRowIndex;
```

### 2. 服务层逻辑
在 `processFieldMappingImageDownloads` 方法中：
```java
// 如果指定了targetRowIndex，只处理指定行的数据
List<FeishuBitableResponse.Record> recordsToProcess = records;
if (request.getTargetRowIndex() != null && request.getTargetRowIndex() > 0) {
    int targetIndex = request.getTargetRowIndex() - 1; // 转换为0基索引
    if (targetIndex < records.size()) {
        recordsToProcess = Collections.singletonList(records.get(targetIndex));
        logger.info("指定处理第 {} 行数据，记录ID: {}", request.getTargetRowIndex(), 
                   recordsToProcess.get(0).getRecordId());
    } else {
        logger.warn("指定的行索引 {} 超出范围，总记录数: {}，将不处理任何记录", 
                   request.getTargetRowIndex(), records.size());
        recordsToProcess = Collections.emptyList();
    }
} else {
    logger.info("未指定targetRowIndex，处理所有 {} 条记录", records.size());
}
```

## 使用场景

### 1. 测试和调试
- 在开发阶段，只处理第一行数据进行快速测试
- 验证字段映射关系是否正确

### 2. 分批处理
- 对于大量数据，可以逐行处理避免超时
- 结合循环调用实现自定义的分批策略

### 3. 特定记录处理
- 只处理表格中的特定行
- 避免重复处理已经处理过的数据

### 4. 错误恢复
- 当批量处理中断时，可以从指定行继续处理
- 精确定位问题记录

## 注意事项

### 1. 索引从1开始
- `targetRowIndex=1` 表示第1行
- `targetRowIndex=2` 表示第2行
- 符合用户的直观理解

### 2. 超出范围处理
- 当指定的行索引超出实际记录数时，返回空结果
- 不会抛出错误，保证API的稳定性

### 3. 与其他参数的兼容性
- 可以与 `pageSize` 参数配合使用
- 可以与 `viewId`、`filter` 等参数配合使用
- 不影响现有的字段映射逻辑

### 4. 性能优化
- 只处理指定行时，仍然需要获取所有记录
- 但只对指定行进行图片下载和处理
- 适合小到中等规模的数据表

## 总结

`targetRowIndex` 功能为字段映射图片上传提供了更精确的控制能力，使用户能够：

1. ✅ **精确控制**：只处理指定行的数据
2. ✅ **灵活使用**：默认处理所有行，指定时处理单行
3. ✅ **错误容忍**：超出范围时优雅处理
4. ✅ **向后兼容**：不影响现有功能和API调用

这个功能特别适合测试、调试、分批处理和错误恢复等场景，大大提升了API的实用性和灵活性。
