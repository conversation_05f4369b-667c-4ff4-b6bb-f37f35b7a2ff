#!/bin/bash

# 测试多维表格结构的脚本
# 用于查看实际的字段名称和数据格式

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试数据配置
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  多维表格结构分析${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 使用现有的接口来获取表格数据
SERVER_URL="http://localhost:8080"

echo -e "${YELLOW}1. 启动服务器...${NC}"
nohup java -jar target/ziniao-ai-demo-1.0.0.jar > logs/app.log 2>&1 &
sleep 10

echo -e "${YELLOW}2. 检查服务器状态...${NC}"
if curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器启动失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}3. 获取表格数据结构...${NC}"

# 使用现有的图片下载接口来获取数据，但不下载图片
RESPONSE=$(curl -s -X POST "$SERVER_URL/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 1,
    "downloadToLocal": false,
    "updateBitableWithLocalUrl": false,
    "includeImageDetails": true
  }')

echo "响应数据:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"

echo ""
echo -e "${YELLOW}4. 分析字段结构...${NC}"

# 提取字段信息
if echo "$RESPONSE" | grep -q '"records"'; then
    echo "找到记录数据，分析字段..."
    
    # 提取第一条记录的字段
    FIELDS=$(echo "$RESPONSE" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'records' in data['data'] and len(data['data']['records']) > 0:
        record = data['data']['records'][0]
        if 'fields' in record:
            fields = record['fields']
            print('字段列表:')
            for field_name, field_value in fields.items():
                field_type = type(field_value).__name__
                if isinstance(field_value, list) and len(field_value) > 0:
                    first_item_type = type(field_value[0]).__name__
                    print(f'  {field_name}: {field_type} (第一个元素: {first_item_type})')
                    if isinstance(field_value[0], dict):
                        print(f'    包含键: {list(field_value[0].keys())}')
                else:
                    print(f'  {field_name}: {field_type}')
                    if field_type == 'str' and len(str(field_value)) > 50:
                        print(f'    值: {str(field_value)[:50]}...')
                    else:
                        print(f'    值: {field_value}')
        else:
            print('记录中没有fields字段')
    else:
        print('响应中没有找到记录数据')
except Exception as e:
    print(f'解析错误: {e}')
" 2>/dev/null)

    echo "$FIELDS"
else
    echo "响应中没有找到记录数据"
fi

echo ""
echo -e "${YELLOW}5. 检查特定字段...${NC}"

# 检查是否存在我们要找的字段
if echo "$RESPONSE" | grep -q "👚上装正面图"; then
    echo -e "${GREEN}✅ 找到字段: 👚上装正面图${NC}"
    
    # 提取该字段的详细信息
    FIELD_DETAIL=$(echo "$RESPONSE" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'records' in data['data'] and len(data['data']['records']) > 0:
        for record in data['data']['records']:
            if 'fields' in record and '👚上装正面图' in record['fields']:
                field_value = record['fields']['👚上装正面图']
                print(f'字段值类型: {type(field_value).__name__}')
                print(f'字段值内容: {field_value}')
                break
except Exception as e:
    print(f'解析错误: {e}')
" 2>/dev/null)
    
    echo "$FIELD_DETAIL"
else
    echo -e "${RED}❌ 没有找到字段: 👚上装正面图${NC}"
    echo "可能的原因:"
    echo "1. 字段名称不正确"
    echo "2. 该字段在当前记录中为空"
    echo "3. 视图设置问题"
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo "分析完成！"

# 停止服务器
pkill -f "java.*ziniao"
