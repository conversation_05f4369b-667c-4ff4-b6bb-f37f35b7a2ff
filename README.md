# 紫鸟AI穿衣API Spring Boot Demo

这是一个基于Spring Boot的紫鸟平台AI穿衣API集成示例项目，集成了官方SDK、Swagger文档和Knife4j。

## 🚀 项目特色

- ✅ **官方SDK集成** - 使用紫鸟官方提供的Java SDK
- ✅ **Spring Boot架构** - 现代化的Spring Boot应用
- ✅ **令牌自动管理** - 自动获取和缓存应用令牌
- ✅ **完整API封装** - 封装所有AI穿衣相关接口
- ✅ **Swagger文档** - 集成Knife4j，提供完整的API文档
- ✅ **异步任务支持** - 支持长时间处理的异步任务
- ✅ **完整测试用例** - 包含单元测试和集成测试

## 📁 项目结构

```
src/
├── main/
│   ├── java/com/ziniao/
│   │   ├── ZiniaoAiDemoApplication.java    # Spring Boot主类
│   │   ├── config/
│   │   │   ├── ApiConfig.java              # API配置
│   │   │   └── SwaggerConfig.java          # Swagger配置
│   │   ├── controller/
│   │   │   ├── ClothingController.java     # AI穿衣控制器
│   │   │   └── TokenController.java        # 令牌管理控制器
│   │   ├── model/
│   │   │   ├── ClothingRequest.java        # 请求模型
│   │   │   ├── ClothingResponse.java       # 响应模型
│   │   │   └── TokenResponse.java          # 令牌响应模型
│   │   ├── service/
│   │   │   ├── ClothingService.java        # AI穿衣服务
│   │   │   └── TokenService.java           # 令牌管理服务
│   │   └── demo/
│   │       └── ClothingDemo.java           # 演示类
│   └── resources/
│       ├── application.yml                 # 应用配置
│       └── markdown/home.md                # API文档首页
└── test/
    └── java/com/ziniao/service/
        └── ZiniaoApiServiceTest.java       # 单元测试
```

## 🚀 快速开始

### 1. 确认SDK文件

确保项目根目录有 `sdk-java-5.0.6.jar` 文件（紫鸟官方SDK）：

```bash
ls -la sdk-java-5.0.6.jar
```

### 2. 配置API密钥

编辑 `src/main/resources/application.yml` 文件，填入您的API配置：

```yaml
ziniao:
  api:
    app-id: your_app_id_here          # 您的应用ID
    private-key: your_private_key_here # 您的应用私钥
```

### 3. 启动应用

```bash
# 使用Maven启动（推荐）
mvn spring-boot:run

# 或者先编译再运行
mvn clean package
java -jar target/ziniao-ai-demo-1.0.0.jar

# 简单编译测试（验证代码正确性）
./test-compile.sh
```

### 3. 访问API文档

启动后访问以下地址：

- **Knife4j文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui/
- **应用首页**: http://localhost:8080

### 4. 运行测试

```bash
mvn test
```

## 🔧 主要功能

### 1. 令牌管理

```bash
# 获取应用令牌
GET /api/token/app

# 刷新应用令牌
POST /api/token/app/refresh
```

### 2. AI穿衣处理

```bash
# AI穿衣处理
POST /api/clothing/process

# 查询任务状态
GET /api/clothing/task/{taskId}

# 等待任务完成
POST /api/clothing/task/{taskId}/wait
```

### 3. 编程方式调用

```java
@Autowired
private ClothingService clothingService;

// 创建请求
ClothingRequest request = new ClothingRequest();
request.setPersonImage("https://example.com/person.jpg");
request.setClothingImage("https://example.com/clothing.jpg");
request.setClothingType("上衣");

// 调用服务
ClothingResponse response = clothingService.processClothing(request);
```

### 4. 异步任务处理

```java
// 提交任务
ClothingResponse initialResponse = clothingService.processClothing(request);
String taskId = initialResponse.getData().getTaskId();

// 等待任务完成
ClothingResponse finalResponse = clothingService.waitForTaskCompletion(
    taskId,
    300000, // 最大等待5分钟
    5000    // 每5秒轮询一次
);
```

## API参数说明

### 请求参数 (ClothingRequest)

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| personImage | String | 是 | 人物图片URL或base64编码 |
| clothingImage | String | 是 | 服装图片URL或base64编码 |
| clothingType | String | 是 | 服装类型（上衣、下装等） |
| style | String | 否 | 风格参数 |
| quality | String | 否 | 质量参数 |

### 响应参数 (ClothingResponse)

| 参数 | 类型 | 说明 |
|------|------|------|
| code | int | 响应状态码 |
| message | String | 响应消息 |
| requestId | String | 请求ID |
| data | ClothingData | 响应数据 |

### 响应数据 (ClothingData)

| 参数 | 类型 | 说明 |
|------|------|------|
| resultImage | String | 结果图片URL |
| taskId | String | 任务ID（异步处理时使用） |
| status | String | 任务状态 |
| progress | int | 处理进度 |

## 注意事项

1. **API密钥安全**：请妥善保管您的API密钥，不要将其提交到版本控制系统中。

2. **图片格式**：支持的图片格式请参考官方文档。

3. **请求频率**：请注意API的调用频率限制，避免过于频繁的请求。

4. **错误处理**：示例代码包含了基本的错误处理，实际使用时请根据需要完善。

5. **日志记录**：项目使用SLF4J + Logback进行日志记录，日志文件保存在 `logs/` 目录下。

## 依赖说明

- **Apache HttpClient 4.5.14**：用于HTTP请求
- **Jackson 2.15.2**：用于JSON序列化/反序列化
- **SLF4J + Logback**：用于日志记录
- **JUnit 4.13.2**：用于单元测试

## 故障排除

1. **连接超时**：检查网络连接和API服务状态
2. **认证失败**：检查API密钥和App ID是否正确
3. **参数错误**：检查请求参数是否符合API要求
4. **图片格式问题**：确保图片格式和大小符合要求

## 更多信息

请参考紫鸟开放平台官方文档：
- API调用指南：https://open.ziniao.com/docSupport?docId=233
- AI穿衣文档：https://open.ziniao.com/docSupport?docId=207
- Java SDK示例：https://open.ziniao.com/docSupport?docId=130
