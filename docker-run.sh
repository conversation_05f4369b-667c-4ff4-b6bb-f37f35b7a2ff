#!/bin/bash

# 紫鸟AI穿衣演示 Docker运行脚本
# 此脚本使用适当的配置运行Docker容器

set -e  # 遇到错误时退出

echo "=== 紫鸟AI穿衣演示 - Docker运行脚本 ==="
echo ""

# 配置
IMAGE_NAME="ziniao-ai-demo"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="ziniao-ai-demo-container"
HOST_PORT="8080"
CONTAINER_PORT="8080"

# 输出颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 彩色输出函数
print_status() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查Docker是否运行
print_status "检查Docker状态..."
if ! docker info &> /dev/null; then
    print_error "Docker未运行。请先启动Docker。"
    exit 1
fi

# 检查镜像是否存在
print_status "检查Docker镜像是否存在..."
if ! docker image inspect "$FULL_IMAGE_NAME" &> /dev/null; then
    print_error "Docker镜像 '$FULL_IMAGE_NAME' 未找到。"
    print_status "请先构建镜像: ./docker-build.sh"
    exit 1
fi

print_success "找到Docker镜像: $FULL_IMAGE_NAME"

# 停止并删除现有容器（如果存在）
if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    print_status "停止并删除现有容器..."
    docker stop "$CONTAINER_NAME" &> /dev/null || true
    docker rm "$CONTAINER_NAME" &> /dev/null || true
fi

# 检查端口是否可用
if command -v lsof &> /dev/null && lsof -Pi :$HOST_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "端口 $HOST_PORT 已被占用。容器可能无法正常启动。"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "用户取消操作。"
        exit 1
    fi
fi

# 检查.env文件是否存在
ENV_FILE=".env"
if [ -f "$ENV_FILE" ]; then
    print_status "找到环境变量文件: $ENV_FILE"
    ENV_FILE_OPTION="--env-file $ENV_FILE"
else
    print_warning "未找到环境变量文件: $ENV_FILE"
    ENV_FILE_OPTION=""
fi

# 运行容器
print_status "启动Docker容器..."
echo ""

docker run \
    --name "$CONTAINER_NAME" \
    --publish "$HOST_PORT:$CONTAINER_PORT" \
    --detach \
    --restart unless-stopped \
    $ENV_FILE_OPTION \
    --env JAVA_OPTS="-Xmx512m -Xms256m" \
    "$FULL_IMAGE_NAME"

if [ $? -eq 0 ]; then
    print_success "容器启动成功！"
    echo ""
    
    # 等待应用启动
    print_status "等待应用启动..."
    sleep 5
    
    # 检查容器状态
    if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_success "容器正在运行: $CONTAINER_NAME"
        
        # 显示容器信息
        print_status "容器信息:"
        docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        echo ""
        print_status "应用访问地址:"
        echo "  • 主应用: http://localhost:$HOST_PORT"
        echo "  • API文档: http://localhost:$HOST_PORT/doc.html"
        echo "  • Swagger UI: http://localhost:$HOST_PORT/swagger-ui/"
        echo "  • 健康检查: http://localhost:$HOST_PORT/actuator/health"
        
        echo ""
        print_status "有用的命令:"
        echo "  • 查看日志: docker logs $CONTAINER_NAME"
        echo "  • 跟踪日志: docker logs -f $CONTAINER_NAME"
        echo "  • 停止容器: docker stop $CONTAINER_NAME"
        echo "  • 删除容器: docker rm $CONTAINER_NAME"
        
        # 测试应用是否响应
        print_status "测试应用健康状态..."
        sleep 10
        if command -v curl &> /dev/null; then
            if curl -f -s http://localhost:$HOST_PORT/ > /dev/null 2>&1; then
                print_success "应用健康且正在响应！"
            else
                print_warning "应用可能仍在启动中。请查看日志: docker logs $CONTAINER_NAME"
            fi
        else
            print_warning "未安装curl，无法测试应用响应。请手动访问 http://localhost:$HOST_PORT"
        fi
        
    else
        print_error "容器启动失败。请查看日志: docker logs $CONTAINER_NAME"
        exit 1
    fi
    
else
    print_error "启动容器失败"
    exit 1
fi
