# 🎯 字段映射图片上传优化功能使用指南

## 📋 功能概述

优化后的字段映射图片上传功能提供了更灵活的字段映射和精确的行处理能力：

### ✨ 新增特性

1. **灵活的字段映射**：支持不同的字段名称映射，前面字段获取图片，后面字段回写图片URL
2. **精确行处理**：可以指定处理特定行（通过行索引或记录ID）
3. **记录信息查询**：提供专门的接口获取表格记录信息，帮助确定要处理的行

## 🔧 API接口

### 1. 获取表格记录信息

**接口地址**: `POST /api/feishu/bitable/records/info`

**功能**: 获取飞书多维表格的记录信息，包括记录ID和行索引

**请求参数**:
```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "pageSize": 10
}
```

**响应示例**:
```json
{
  "success": true,
  "totalRecords": 3,
  "records": [
    {
      "rowIndex": 1,
      "recordId": "recqwIwhc6",
      "createdTime": 1721846400000,
      "lastModifiedTime": 1721846400000,
      "fieldsPreview": {
        "👚上装正面图": [...],
        "👚上装背面图": [...],
        "使用垫图粘贴": [...]
      },
      "totalFields": 8
    },
    {
      "rowIndex": 2,
      "recordId": "recABC123",
      "createdTime": 1721846500000,
      "lastModifiedTime": 1721846500000,
      "fieldsPreview": {...},
      "totalFields": 8
    }
  ],
  "message": "获取记录信息成功"
}
```

### 2. 字段映射图片上传

**接口地址**: `POST /api/feishu/bitable/images/field-mapping-upload`

**功能**: 根据字段映射关系，从源图片字段下载图片并将本地URL写入目标字段

**核心参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| fieldMapping | Map<String,String> | ✅ | 字段映射关系，key为源字段，value为目标字段 | 见下方示例 |
| targetRowIndex | Integer | ❌ | 指定要处理的行索引（从1开始） | 1 |
| targetRecordId | String | ❌ | 指定要处理的记录ID | "recqwIwhc6" |

## 📝 使用示例

### 示例1: 获取表格记录信息

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/records/info" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "pageSize": 5
  }'
```

### 示例2: 灵活的字段映射

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url",
      "使用垫图粘贴": "使用垫图粘贴url"
    },
    "updateBitableWithLocalUrl": true
  }'
```

### 示例3: 处理指定行（使用行索引）

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "targetRowIndex": 1,
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "使用垫图粘贴": "使用垫图粘贴url"
    },
    "updateBitableWithLocalUrl": true
  }'
```

### 示例4: 处理指定行（使用记录ID）

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "targetRecordId": "recqwIwhc6",
    "fieldMapping": {
      "👚上装正面图": "前面图片链接",
      "👚上装背面图": "后面图片链接",
      "使用垫图粘贴": "垫图链接"
    },
    "updateBitableWithLocalUrl": true
  }'
```

## 🎯 如何获取行参数

### 方法1: 在飞书表格中查看

1. 打开飞书多维表格
2. 查看要处理的行号（从第1行开始计数，不包括表头）
3. 在API请求中使用 `targetRowIndex` 参数

### 方法2: 通过API获取

1. 调用 `/api/feishu/bitable/records/info` 接口
2. 从响应的 `records` 数组中获取：
   - `rowIndex`: 行索引（从1开始）
   - `recordId`: 记录ID
3. 在字段映射接口中使用获取到的参数

### 方法3: 使用记录ID（推荐）

记录ID是飞书表格中每行数据的唯一标识，比行索引更稳定：

```javascript
// 从记录信息响应中提取记录ID
const recordsInfo = response.records;
const firstRecordId = recordsInfo[0].recordId;  // "recqwIwhc6"
const secondRecordId = recordsInfo[1].recordId; // "recABC123"
```

## 🔄 处理优先级

当同时指定多个目标参数时，处理优先级如下：

1. **targetRecordId** (最高优先级)
2. **targetRowIndex**
3. **处理所有行** (默认行为)

## ✅ 优化特性总结

| 特性 | 说明 | 优势 |
|------|------|------|
| 灵活字段映射 | 支持不同的字段名称映射 | 前面字段获取图片，后面字段回写URL，无需强制同步 |
| 精确行处理 | 支持行索引和记录ID | 只处理指定行，提高效率 |
| 记录信息查询 | 专门的接口获取行信息 | 方便确定要处理的行参数 |
| 智能优先级 | 多种目标参数的优先级处理 | 灵活选择处理方式 |

## 🚀 快速开始

1. **启动应用**: 运行 `ZiniaoAiDemoApplication.java`
2. **获取记录信息**: 调用记录信息接口了解表格结构
3. **执行字段映射**: 根据需要处理特定行或所有行
4. **测试脚本**: 运行 `./test-field-mapping-optimized.sh` 查看完整示例

## 📞 技术支持

如有问题，请查看日志输出或联系技术支持团队。
