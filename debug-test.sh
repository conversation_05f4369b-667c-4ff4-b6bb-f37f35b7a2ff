#!/bin/bash

# 调试测试脚本
echo "开始调试测试..."

curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-by-record" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "LgSGbXDBsa0yCsshXujcN7WUnCh",
    "tableId": "tblUkixc2NcTbpmX",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-SKC_url"
    },
    "recordIds": ["recuJ5xW8OxTyL"],
    "summaryOnly": false,
    "processId": "debug_test_001"
  }' | jq '.'

echo "调试测试完成"
