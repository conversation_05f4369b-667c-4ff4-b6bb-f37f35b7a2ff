# 文件上传功能说明

## 功能概述

已成功为紫鸟AI穿衣Demo项目添加了完整的文件上传功能，支持200MB大文件上传，并返回服务器上的完整访问URL。

## 主要特性

### 1. 文件大小限制
- 最大支持200MB文件上传
- 在Spring Boot配置和自定义验证中都进行了限制

### 2. 文件类型限制
支持的文件类型：
- 图片：jpg, jpeg, png, gif, bmp, webp
- 文档：pdf, doc, docx, txt
- 压缩包：zip, rar
- 视频：mp4, avi, mov
- 音频：mp3, wav

### 3. 文件存储
- 存储路径：`${user.dir}/uploads/` (项目根目录下的uploads文件夹)
- 目录结构：按日期自动分组 `uploads/yyyy/MM/dd/`
- 文件命名：时间戳 + UUID + 原扩展名，避免重名冲突

### 4. URL访问
- 静态资源映射：`/uploads/**` 映射到实际存储目录
- 完整URL格式：`http://localhost:8080/uploads/yyyy/MM/dd/filename`

### 5. 图片URL下载功能
- 支持从外部URL下载图片并保存到服务器
- 自动验证图片格式和大小
- 支持HTTP和HTTPS协议
- 自动从URL或Content-Type推断文件扩展名
- 提供表单参数和JSON两种请求方式

## API接口

### 1. 单文件上传
```
POST /api/file/upload
Content-Type: multipart/form-data
参数: file (文件)
```

响应示例：
```json
{
  "code": 200,
  "message": "上传成功",
  "success": true,
  "data": {
    "originalFileName": "test.txt",
    "fileName": "20250721_020113_97bac048.txt",
    "fileSize": 121,
    "contentType": "text/plain",
    "fileUrl": "http://localhost:8080/uploads/2025/07/21/20250721_020113_97bac048.txt",
    "relativePath": "/uploads/2025/07/21/20250721_020113_97bac048.txt",
    "uploadTime": "2025-07-21 02:01:13"
  }
}
```

### 2. 多文件上传
```
POST /api/file/upload/batch
Content-Type: multipart/form-data
参数: files[] (文件数组)
```

### 3. 文件删除
```
DELETE /api/file/delete?filePath=/uploads/path/to/file
```

### 4. 文件存在性检查
```
GET /api/file/exists?filePath=/uploads/path/to/file
```

### 5. 文件下载
```
GET /api/file/download/path/to/file
```

### 6. 从URL下载图片（表单参数）
```
POST /api/file/download-from-url
Content-Type: application/x-www-form-urlencoded
参数: imageUrl (图片URL地址)
```

### 7. 从URL下载图片（JSON格式）
```
POST /api/file/download-image
Content-Type: application/json
请求体: {
  "imageUrl": "https://example.com/image.jpg",
  "customFileName": "my_image" (可选)
}
```

### 8. 健康检查
```
GET /api/file/health
```

## 配置说明

在 `application.yml` 中的配置：

```yaml
spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 200MB
      max-request-size: 200MB
      file-size-threshold: 2KB

file:
  upload:
    path: ${user.dir}/uploads/
    max-size: 209715200  # 200MB in bytes
    allowed-types: jpg,jpeg,png,gif,bmp,webp,pdf,doc,docx,txt,zip,rar,mp4,avi,mov,mp3,wav
    url-prefix: /uploads/
```

## 测试结果

✅ 单文件上传 - 成功
✅ 多文件上传 - 成功  
✅ 大文件上传 (10MB) - 成功
✅ 文件类型验证 - 成功
✅ 文件访问URL - 成功
✅ 文件删除 - 成功
✅ 文件存在性检查 - 成功
✅ 健康检查 - 成功

## 使用示例

### 上传文件
```bash
curl -X POST -F "file=@example.jpg" http://localhost:8080/api/file/upload
```

### 批量上传
```bash
curl -X POST -F "files=@file1.txt" -F "files=@file2.jpg" http://localhost:8080/api/file/upload/batch
```

### 删除文件
```bash
curl -X DELETE "http://localhost:8080/api/file/delete?filePath=/uploads/2025/07/21/filename.jpg"
```

## 启动说明

使用包含文件上传功能的JAR文件启动：
```bash
java -jar target/ziniao-ai-demo-with-upload.jar
```

或者使用重建脚本：
```bash
./rebuild-with-upload.sh
java -jar target/ziniao-ai-demo-with-upload.jar
```

## API文档

启动应用后，可以通过以下地址查看完整的API文档：
- Knife4j文档：http://localhost:8080/doc.html
- Swagger UI：http://localhost:8080/swagger-ui/

文件上传相关接口在"文件上传服务"分组中。
