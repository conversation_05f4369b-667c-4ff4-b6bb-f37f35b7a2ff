# 紫鸟API配置示例
# 请复制此文件为 config.properties 并填入实际的配置信息

# API基础配置
ziniao.api.base.url=https://open.ziniao.com
ziniao.api.key=your_api_key_here
ziniao.app.id=your_app_id_here

# API端点配置
ziniao.api.clothing.endpoint=/api/ai/clothing
ziniao.api.task.status.endpoint=/api/task/status

# 请求配置
ziniao.api.timeout=30000
ziniao.api.content.type=application/json

# 异步任务配置
ziniao.task.max.wait.time=300000
ziniao.task.poll.interval=5000

# 日志配置
logging.level.com.ziniao=DEBUG
logging.level.org.apache.http=INFO
