# 紫鸟AI穿衣演示 - 快速开始指南

## 🚀 三步启动

### 方法一：使用脚本（推荐）

```bash
# 1. 准备离线环境
./prepare-offline.sh

# 2. 构建Docker镜像
./docker-build.sh

# 3. 运行容器
./docker-run.sh
```

### 方法二：使用Docker Compose

```bash
# 1. 准备离线环境
./prepare-offline.sh

# 2. 配置环境变量
cp .env.example .env
# 编辑.env文件，填入您的API凭据

# 3. 启动应用
docker-compose up -d
```

### 方法三：手动Docker命令

```bash
# 1. 准备离线环境
./prepare-offline.sh

# 2. 构建镜像
docker build -t ziniao-ai-demo:latest .

# 3. 运行容器
docker run -d --name ziniao-demo -p 8080:8080 ziniao-ai-demo:latest
```

## 🌐 访问应用

启动成功后，访问以下地址：

- **主应用**: http://localhost:8080
- **API文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui/
- **健康检查**: http://localhost:8080/actuator/health

## 📋 系统要求

- Docker 20.10+
- 1GB+ 可用内存
- 500MB+ 磁盘空间

## 🔧 配置API凭据

编辑`.env`文件：

```env
ZINIAO_API_APP_ID=您的应用ID
ZINIAO_API_PRIVATE_KEY=您的私钥
```

## 🐛 常见问题

### 端口被占用
```bash
# 使用不同端口
docker run -d --name ziniao-demo -p 8081:8080 ziniao-ai-demo:latest
```

### 查看日志
```bash
docker logs ziniao-demo
```

### 停止容器
```bash
docker stop ziniao-demo
docker rm ziniao-demo
```

## 📚 更多文档

- [DOCKER使用说明.md](DOCKER使用说明.md) - 详细使用说明
- [DOCKER_GUIDE.md](DOCKER_GUIDE.md) - 完整Docker指南
- [README.md](README.md) - 项目主文档

---

**提示**: 首次构建可能需要几分钟时间下载依赖，请耐心等待。
