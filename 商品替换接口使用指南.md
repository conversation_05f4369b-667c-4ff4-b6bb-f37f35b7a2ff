# 商品替换接口使用指南

## 接口概述

商品替换接口用于将一个商品图片替换到另一个场景图片中，实现商品的场景替换效果。

## 接口信息

- **接口路径**: `/api/clothing/shopReplace`
- **请求方式**: `POST`
- **Content-Type**: `application/json`

## 请求参数

| 参数名称 | 参数说明 | 是否必须 | 数据类型 | 示例值 |
|---------|---------|---------|---------|--------|
| imageUrl | 商品原图 | 是 | string | https://example.com/product.jpg |
| sourceImageUrl | 原图抠出来的商品图 | 否 | string | https://example.com/product_mask.jpg |
| targetOriginUrl | 替换的目标原图 | 是 | string | https://example.com/scene.jpg |
| targetImageUrl | 替换的目标原图抠图结果 | 否 | string | https://example.com/scene_mask.jpg |
| denoiseStrength | 生成图变化程度，默认值：0.5，越大越高，浮点数，可选范围 [0,1] | 否 | string | "0.5" |
| imageOutputWidth | 输出尺寸宽度，最长边不能超过4096，最短边不能低于32 | 否 | integer | 1024 |
| imageOutputHeight | 输出尺寸高度，最长边不能超过4096，最短边不能低于32 | 否 | integer | 1024 |
| outputNum | 输出张数，取值范围：[1,4]，默认值：1 | 否 | integer | 1 |

## 请求示例

```bash
curl -X POST "http://localhost:8080/api/clothing/shopReplace" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/96a7a42fc965491dbc625d2db09f2478.png",
    "sourceImageUrl": "",
    "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/9778678b4724467485346248032387ec.png",
    "targetImageUrl": "",
    "denoiseStrength": "0.5",
    "imageOutputWidth": "",
    "imageOutputHeight": "",
    "outputNum": 1
  }'
```

## 响应格式

### 成功响应

```json
{
  "request_id": "xxx",
  "code": "0",
  "msg": "成功",
  "sub_code": null,
  "sub_msg": null,
  "data": {
    "msg": null,
    "code": 200,
    "traceId": "xxx",
    "data": {
      "id": "1945877291232894976"
    },
    "msgKey": null
  },
  "success": true,
  "task_id": null
}
```

### 错误响应

```json
{
  "code": "400",
  "msg": "参数错误",
  "success": false
}
```

## 使用流程

1. **提交任务**: 调用商品替换接口，提交处理任务
2. **获取任务ID**: 从响应中获取任务ID (`data.data.id`)
3. **查询结果**: 使用任务ID查询处理结果

## 查询任务结果

使用返回的任务ID查询处理结果：

```bash
curl -X GET "http://localhost:8080/api/clothing/result/{taskId}"
```

## 测试脚本

项目中提供了测试脚本 `test-shop-replace.sh`，可以直接运行测试：

```bash
./test-shop-replace.sh
```

## 注意事项

1. **图片格式**: 支持常见的图片格式（JPG、PNG、WebP等）
2. **图片大小**: 建议图片大小不超过10MB
3. **处理时间**: 商品替换处理需要一定时间，请耐心等待
4. **输出尺寸**: 如果不指定输出尺寸，将使用原图尺寸
5. **变化程度**: denoiseStrength参数控制生成图的变化程度，建议使用默认值0.5

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |
| 20001 | 授权权限不足 |

## 技术支持

如有问题，请查看日志文件或联系技术支持。
