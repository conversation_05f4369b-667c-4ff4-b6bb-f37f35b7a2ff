# 🚀 按记录ID字段映射图片处理接口使用指南

## 📋 接口概述

新的按记录ID字段映射图片处理接口 `/api/feishu/bitable/images/field-mapping-by-record` 完全解决了原接口的限制问题：

### ✅ 解决的问题

1. **突破500行限制**：通过智能分页自动处理超过500行的数据
2. **精确行ID处理**：支持指定特定记录ID进行处理，只获取需要的数据
3. **数据量优化**：支持摘要模式，大幅减少数据传输量
4. **完全重构**：全新的架构设计，不影响老接口使用

### 🆚 与原接口对比

| 功能特性 | 原接口 | 新接口 |
|---------|--------|--------|
| 数据量限制 | 500行 | 无限制（智能分页） |
| 精确行处理 | ❌ | ✅ 支持recordIds列表 |
| 轻量级响应 | ❌ | ✅ 支持summaryOnly模式 |
| 批量处理 | 基础 | ✅ 优化的批量处理 |
| 错误处理 | 基础 | ✅ 详细错误信息和统计 |
| 性能优化 | 基础 | ✅ 多种优化选项 |

## 🎯 核心功能

### 1. 精确记录ID处理
只处理指定的记录ID，避免处理不必要的数据：

```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url",
    "👚上装背面图": "👚上装背面图url"
  },
  "recordIds": ["recqwIwhc6", "recABC123", "recXYZ789"],
  "summaryOnly": true
}
```

### 2. 智能分页处理
自动处理超过500行的数据，无需手动分页：

```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "enableSmartPaging": true,
  "pageSize": 200,
  "maxPages": 10
}
```

### 3. 轻量级响应模式
减少不必要的数据传输：

```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "summaryOnly": true,
  "skipExistingUrls": true
}
```

## 📝 请求参数详解

### 必需参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `appToken` | String | 多维表格的唯一标识符 | `"MgDxby4r7avigssLQnVcIQzJnm1"` |
| `tableId` | String | 数据表的唯一标识符 | `"tbl4sH8PYHUk36K0"` |
| `fieldMapping` | Object | 字段映射关系 | `{"👚上装正面图": "👚上装正面图url"}` |

### 可选参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `recordIds` | Array | null | 要处理的记录ID列表 |
| `enableSmartPaging` | Boolean | false | 是否启用智能分页 |
| `pageSize` | Integer | 200 | 分页大小（最大500） |
| `maxPages` | Integer | 0 | 最大处理页数（0=无限制） |
| `summaryOnly` | Boolean | false | 是否只返回摘要信息 |
| `batchSize` | Integer | 50 | 批处理大小 |
| `continueOnError` | Boolean | true | 遇到错误时是否继续 |
| `skipExistingUrls` | Boolean | false | 是否跳过已有URL的记录 |
| `processId` | String | 自动生成 | 处理ID，用于日志追踪 |

## 📊 响应格式

### 成功响应示例

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "processId": "by_record_1706745600000",
    "startTime": "2024-01-31T10:00:00",
    "endTime": "2024-01-31T10:01:30",
    "totalProcessingTime": 90000,
    "processedRecords": 5,
    "totalImages": 10,
    "successfulDownloads": 8,
    "failedDownloads": 2,
    "skippedRecords": 0,
    "pagesProcessed": 1,
    "hasMoreData": false,
    "records": [
      {
        "recordId": "recqwIwhc6",
        "processedFields": 2,
        "imageCount": 2,
        "successCount": 2,
        "failureCount": 0,
        "status": "SUCCESS"
      }
    ],
    "performanceStats": {
      "avgProcessingTimePerRecord": 18000,
      "avgDownloadTimePerImage": 9000,
      "apiCallCount": 3,
      "totalDownloadBytes": 819200
    }
  }
}
```

## 🔧 使用场景

### 场景1: 只处理特定行
当你知道具体要处理哪些记录时：

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-by-record" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "recordIds": ["recqwIwhc6", "recABC123"],
    "summaryOnly": true
  }'
```

### 场景2: 处理大数据集
当数据超过500行时：

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-by-record" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "enableSmartPaging": true,
    "pageSize": 500,
    "maxPages": 0,
    "summaryOnly": true
  }'
```

### 场景3: 性能优化处理
当需要最佳性能时：

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-by-record" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "summaryOnly": true,
    "skipExistingUrls": true,
    "maxConcurrentDownloads": 5,
    "batchSize": 100
  }'
```

## 🚨 注意事项

1. **兼容性**：新接口与原接口完全独立，不会影响原接口使用
2. **性能**：处理大量数据时建议使用 `summaryOnly: true`
3. **错误处理**：建议设置 `continueOnError: true` 以提高容错性
4. **监控**：使用 `processId` 参数便于日志追踪和问题排查
5. **限制**：单次请求建议不超过5000行数据，超大数据集建议分批处理

## 📈 性能建议

### 大数据量处理（>1000行）
```json
{
  "enableSmartPaging": true,
  "pageSize": 500,
  "summaryOnly": true,
  "batchSize": 100,
  "maxConcurrentDownloads": 5,
  "skipExistingUrls": true
}
```

### 精确处理（<100行）
```json
{
  "recordIds": ["rec1", "rec2", "rec3"],
  "summaryOnly": false,
  "maxConcurrentDownloads": 3
}
```

## 🔄 迁移指南

### 从原接口迁移

#### 原接口调用
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "pageSize": 100
}
```

#### 新接口等效调用
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "pageSize": 100,
  "summaryOnly": false
}
```

#### 新接口优化调用
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "enableSmartPaging": true,
  "pageSize": 500,
  "summaryOnly": true
}
```

## 🎉 总结

新的按记录ID字段映射图片处理接口提供了：

- ✅ **精确控制**：只处理需要的记录
- ✅ **无限制处理**：突破500行限制
- ✅ **性能优化**：多种优化选项
- ✅ **完全兼容**：不影响现有系统
- ✅ **详细监控**：完整的处理统计和错误信息

立即开始使用新接口，享受更高效的数据处理体验！
