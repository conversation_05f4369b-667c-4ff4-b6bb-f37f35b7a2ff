#!/bin/bash

# 紫鸟AI穿衣API Spring Boot Demo 构建和启动脚本

echo "=== 紫鸟AI穿衣API Spring Boot Demo 构建脚本 ==="

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven未安装，请先安装Maven"
    exit 1
fi

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "错误: Java未安装，请先安装Java 8或更高版本"
    exit 1
fi

echo "Java版本:"
java -version

echo "Maven版本:"
mvn -version

echo ""
echo "=== 开始构建项目 ==="

# 清理并编译
echo "1. 清理项目..."
mvn clean

echo "2. 编译项目..."
mvn compile

echo "3. 运行测试..."
mvn test

echo "4. 打包项目..."
mvn package -DskipTests

if [ $? -eq 0 ]; then
    echo ""
    echo "=== 构建成功 ==="
    echo "生成的JAR文件: target/ziniao-ai-demo-1.0.0.jar"
    echo ""
    echo "启动应用:"
    echo "java -jar target/ziniao-ai-demo-1.0.0.jar"
    echo ""
    echo "或者使用Maven启动:"
    echo "mvn spring-boot:run"
    echo ""
    echo "启动后访问:"
    echo "- API文档: http://localhost:8080/doc.html"
    echo "- Swagger UI: http://localhost:8080/swagger-ui/"
    echo "- 应用首页: http://localhost:8080"
    echo ""
    echo "注意: 请先在 application.yml 中配置您的API密钥"
else
    echo ""
    echo "=== 构建失败 ==="
    echo "请检查错误信息并修复问题"
    exit 1
fi
