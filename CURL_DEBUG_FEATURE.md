# 🔧 Curl调试功能说明

## 功能概述

为了方便调试和了解实际的API调用情况，我们为每个官方API调用添加了curl命令打印功能。每次调用紫鸟官方API时，系统会自动在日志中打印等效的curl命令。

## 功能特点

### 1. 自动打印curl命令
- ✅ **获取令牌API**: 打印获取应用令牌的curl命令
- ✅ **AI穿衣API**: 打印AI穿衣请求的curl命令
- ✅ **任务查询API**: 打印任务状态查询的curl命令（如果使用）

### 2. 安全性考虑
- 🔒 **隐藏敏感信息**: 默认版本隐藏令牌和私钥
- 🔍 **调试版本**: DEBUG级别显示完整信息（包含真实令牌）
- 📝 **模板版本**: 提供可复制的模板格式

## 日志输出示例

### 获取令牌API
```
2025-07-19 16:53:18.972 [http-nio-18088-exec-1] INFO  com.ziniao.service.TokenService - 等效的获取令牌curl命令:
curl -X POST "https://sbappstoreapi.ziniao.com/auth/get_app_token" \
  -H "Content-Type: application/json" \
  -H "app-id: 202507121393517203126611968" \
  -H "private-key: [PRIVATE_KEY]" \
  -d '{}'
```

### AI穿衣API
```
2025-07-19 16:53:22.225 [http-nio-18088-exec-1] INFO  com.ziniao.service.ClothingService - 等效的官方API调用curl命令:
curl -X POST "https://sbappstoreapi.ziniao.com/linkfox-ai/image/v2/make/fittingRoom" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 2c9dc0fdcf800d290fb757125e7224ec" \
  -d '{"downImageUrl":"","downOriginUrl":"https://cdn.linkfox.com/ai-site/test/workbench/down-true3.jpg","modelImageUrl":"https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png","modelMaskImageUrl":"","outputNum":1,"upperImageUrl":"","upperOriginUrl":"https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp"}'
```

### 简化版本（隐藏令牌）
```
2025-07-19 16:53:22.225 [http-nio-18088-exec-1] INFO  com.ziniao.service.ClothingService - 简化版curl命令（隐藏令牌）:
curl -X POST "https://sbappstoreapi.ziniao.com/linkfox-ai/image/v2/make/fittingRoom" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [YOUR_TOKEN]" \
  -d '{"downImageUrl":"","downOriginUrl":"https://cdn.linkfox.com/ai-site/test/workbench/down-true3.jpg","modelImageUrl":"https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png","modelMaskImageUrl":"","outputNum":1,"upperImageUrl":"","upperOriginUrl":"https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp"}'
```

## 使用场景

### 1. 调试API调用
- 🐛 **问题排查**: 当API调用失败时，可以复制curl命令直接测试
- 🔍 **参数验证**: 检查发送给官方API的参数是否正确
- 📊 **性能分析**: 了解实际的网络请求内容

### 2. 文档和教学
- 📚 **API文档**: 为开发者提供实际的curl调用示例
- 🎓 **学习参考**: 帮助理解官方API的调用方式
- 🔄 **接口对比**: 对比不同参数下的API调用差异

### 3. 独立测试
- 🧪 **脱离应用测试**: 可以独立于应用直接测试官方API
- 🔧 **快速验证**: 快速验证API参数和响应
- 📝 **文档生成**: 自动生成API调用文档

## 技术实现

### 代码位置
- **TokenService**: `printTokenCurlCommand()` 方法
- **ClothingService**: `printCurlCommand()` 方法

### 实现特点
- 🚀 **自动触发**: 每次API调用前自动执行
- 🛡️ **异常安全**: curl生成失败不影响正常API调用
- 📝 **格式化输出**: 多行格式，便于阅读和复制
- 🔒 **安全分级**: 不同日志级别显示不同详细程度

## 配置说明

### 日志级别控制
```yaml
logging:
  level:
    com.ziniao.service.TokenService: INFO    # 显示隐藏私钥的版本
    com.ziniao.service.ClothingService: INFO # 显示隐藏令牌的版本
    
    # 如需显示完整信息（包含敏感数据），设置为DEBUG
    # com.ziniao.service.TokenService: DEBUG
```

### 生产环境建议
- 🔒 **隐藏敏感信息**: 保持INFO级别，避免泄露令牌和私钥
- 📊 **性能考虑**: 如不需要调试，可以设置为WARN级别
- 💾 **日志管理**: 注意日志文件大小，curl命令会增加日志量

## 注意事项

1. **安全性**: 生产环境请确保不要将包含真实令牌的日志暴露给外部
2. **性能**: curl命令生成会略微增加日志处理时间
3. **日志大小**: 每次API调用会产生额外的日志内容
4. **令牌时效**: 日志中的令牌有时效性，复制使用时注意有效期

## 使用示例

### 复制curl命令进行独立测试
1. 从日志中复制curl命令
2. 替换 `[YOUR_TOKEN]` 为实际令牌
3. 在终端中执行测试

```bash
# 示例：测试AI穿衣API
curl -X POST "https://sbappstoreapi.ziniao.com/linkfox-ai/image/v2/make/fittingRoom" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_actual_token_here" \
  -d '{"upperOriginUrl":"https://example.com/upper.jpg","modelImageUrl":"https://example.com/model.jpg","outputNum":1}'
```

这个功能大大提升了API调试和问题排查的效率！
