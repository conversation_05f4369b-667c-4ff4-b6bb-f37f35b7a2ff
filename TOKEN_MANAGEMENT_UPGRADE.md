# 🔐 智能令牌管理系统升级

## 问题背景

之前遇到的问题：
```json
{
    "requestId": null,
    "code": "20001",
    "msg": "错误, code:20001, msg:授权权限不足",
    "subCode": null,
    "subMsg": null,
    "data": null,
    "success": null,
    "taskId": null
}
```

**根本原因**：令牌过期后没有自动刷新机制，导致API调用失败。

## 解决方案

### 1. 智能令牌缓存机制

#### 替换Spring Cache
- ❌ **之前**：使用 `@Cacheable` 注解，无法控制过期时间
- ✅ **现在**：自定义缓存机制，基于令牌实际过期时间

#### 核心字段
```java
private String cachedToken;           // 缓存的令牌
private LocalDateTime tokenExpireTime; // 令牌过期时间
private final Object tokenLock = new Object(); // 线程安全锁
```

### 2. 智能过期检测

#### 提前刷新策略
```java
// 提前5分钟刷新令牌，避免在使用时过期
LocalDateTime refreshTime = tokenExpireTime.minus(5, ChronoUnit.MINUTES);
if (now.isAfter(refreshTime)) {
    // 刷新令牌
}
```

#### 过期时间管理
- ✅ **自动解析**：从API响应中解析 `expiresIn` 字段（秒数）
- ✅ **默认兜底**：如果没有过期时间，默认设置为2小时
- ✅ **精确计算**：基于当前时间计算准确的过期时间

### 3. 令牌过期重试机制

#### 错误检测
```java
private boolean isTokenExpiredError(CommonResponse response) {
    // 检查错误码 20001
    if ("20001".equals(String.valueOf(response.getCode()))) {
        return true;
    }
    
    // 检查错误消息关键词
    String errorMsg = response.getErrorMsg();
    if (errorMsg != null) {
        String lowerMsg = errorMsg.toLowerCase();
        return lowerMsg.contains("授权权限不足") || 
               lowerMsg.contains("token") && lowerMsg.contains("expired") ||
               lowerMsg.contains("unauthorized");
    }
    
    return false;
}
```

#### 自动重试流程
1. **首次调用**：使用缓存的令牌调用API
2. **错误检测**：如果返回令牌过期错误
3. **强制刷新**：清除缓存，重新获取令牌
4. **重新调用**：使用新令牌重试API调用
5. **更新日志**：打印新的curl命令

## 实现效果

### 第一次调用（获取新令牌）
```
2025-07-19 17:29:40.691 [http-nio-18088-exec-1] INFO  TokenService - 获取令牌响应: {"expiresIn":7200,"appAuthToken":"3fb88ebd796b876713897c09a88dfc7e"}
2025-07-19 17:29:40.694 [http-nio-18088-exec-1] INFO  TokenService - 成功获取应用令牌，过期时间: 2025-07-19T19:29:40.694180
```

### 第二次调用（使用缓存）
```
2025-07-19 17:32:17.926 [http-nio-18088-exec-2] DEBUG TokenService - 令牌有效，过期时间: 2025-07-19T19:29:40.694180
2025-07-19 17:32:17.926 [http-nio-18088-exec-2] DEBUG TokenService - 使用缓存的令牌
```

### 令牌过期时（自动重试）
```
2025-07-19 XX:XX:XX.XXX [http-nio-18088-exec-X] WARN  ClothingService - 检测到令牌过期错误，尝试刷新令牌并重试: 授权权限不足
2025-07-19 XX:XX:XX.XXX [http-nio-18088-exec-X] INFO  ClothingService - 令牌已刷新，使用新令牌重试API调用
```

## 核心优势

### 1. 🚀 性能优化
- **缓存复用**：有效令牌直接使用，避免重复获取
- **提前刷新**：提前5分钟刷新，避免使用时过期
- **线程安全**：使用同步锁，支持并发访问

### 2. 🛡️ 可靠性提升
- **自动重试**：令牌过期时自动刷新并重试
- **错误识别**：准确识别多种令牌过期错误
- **兜底机制**：默认过期时间防止无限缓存

### 3. 🔍 调试友好
- **详细日志**：记录令牌获取、使用、过期等关键信息
- **Curl输出**：每次API调用都输出等效curl命令
- **状态透明**：清楚显示令牌状态和过期时间

### 4. 🔧 维护简单
- **配置灵活**：可通过日志级别控制详细程度
- **代码清晰**：逻辑分离，易于理解和维护
- **扩展性好**：易于添加新的错误类型检测

## 技术实现

### 核心方法

#### 1. 智能获取令牌
```java
public String getAppToken() throws Exception {
    synchronized (tokenLock) {
        if (isTokenValid()) {
            return cachedToken;
        }
        return refreshToken();
    }
}
```

#### 2. 令牌有效性检查
```java
private boolean isTokenValid() {
    if (cachedToken == null || tokenExpireTime == null) {
        return false;
    }
    
    LocalDateTime refreshTime = tokenExpireTime.minus(5, ChronoUnit.MINUTES);
    return LocalDateTime.now().isBefore(refreshTime);
}
```

#### 3. API调用重试逻辑
```java
CommonResponse response = getOpenClient().executeAppToken(apiRequest, appToken);

if (!response.isSuccess() && isTokenExpiredError(response)) {
    String newToken = tokenService.forceRefreshToken();
    response = getOpenClient().executeAppToken(apiRequest, newToken);
}
```

## 使用说明

### 正常使用
- 无需任何额外操作
- 系统自动管理令牌生命周期
- 透明处理令牌过期和刷新

### 强制刷新
```java
// 通过API强制刷新令牌
GET /api/token/refresh

// 或在代码中调用
tokenService.forceRefreshToken();
```

### 日志配置
```yaml
logging:
  level:
    com.ziniao.service.TokenService: DEBUG  # 显示详细令牌管理日志
    com.ziniao.service.ClothingService: INFO # 显示API调用日志
```

## 测试验证

### 功能验证
✅ **令牌获取**：首次调用自动获取令牌  
✅ **缓存复用**：后续调用使用缓存令牌  
✅ **过期检测**：准确识别令牌过期错误  
✅ **自动重试**：过期时自动刷新并重试  
✅ **线程安全**：并发调用正常工作  

### 性能验证
✅ **响应速度**：缓存令牌调用速度快  
✅ **资源使用**：避免不必要的令牌获取请求  
✅ **稳定性**：长时间运行无令牌过期问题  

## 总结

通过实现智能令牌管理系统，彻底解决了令牌过期导致的API调用失败问题：

1. **自动化**：无需手动管理令牌生命周期
2. **智能化**：基于实际过期时间的缓存策略
3. **可靠性**：自动重试机制确保API调用成功
4. **透明性**：详细的日志和curl输出便于调试

现在系统可以稳定运行，不再出现 `"授权权限不足"` 的错误！
