# 🎯 字段映射图片上传功能使用指南

## 📋 功能概述

字段映射图片上传功能允许您通过自定义字段映射关系，从源图片字段下载图片并将本地URL写入目标字段中。这个功能特别适用于需要将图片本地化并更新对应URL字段的场景。

### 🌟 核心特性

- ✅ **自定义字段映射**：支持任意字段名称的映射关系
- ✅ **批量处理**：一次请求可处理多个字段映射
- ✅ **智能识别**：自动识别图片字段类型（附件/URL字符串）
- ✅ **本地存储**：下载图片到服务器本地存储
- ✅ **自动回写**：将本地URL自动写入目标字段
- ✅ **并发控制**：支持并发下载数量控制
- ✅ **错误处理**：完整的错误处理和重试机制

## 🚀 API接口

### 接口地址
```
POST /api/feishu/bitable/images/field-mapping-upload
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| appToken | String | ✅ | 多维表格的唯一标识符 | "MgDxby4r7avigssLQnVcIQzJnm1" |
| tableId | String | ✅ | 数据表的唯一标识符 | "tbl4sH8PYHUk36K0" |
| fieldMapping | Map | ✅ | 字段映射关系 | {"👚上装正面图": "👚上装正面图url"} |
| recordId | String | ❌ | 指定记录ID，不填则处理所有记录 | "recqwIwhc6" |
| viewId | String | ❌ | 视图ID，不填则使用默认视图 | "vewgI30A6c" |
| filter | String | ❌ | 筛选条件 | "CurrentValue.[状态] = \"待处理\"" |
| pageSize | Integer | ❌ | 分页大小，默认100 | 10 |
| pageToken | String | ❌ | 分页标记 | "recqwIwhc6" |
| downloadTimeout | Integer | ❌ | 下载超时时间（秒），默认30 | 60 |
| maxConcurrentDownloads | Integer | ❌ | 最大并发下载数，默认5 | 3 |
| updateBitableWithLocalUrl | Boolean | ❌ | 是否写回本地URL，默认true | true |
| includeImageDetails | Boolean | ❌ | 是否包含图片详细信息，默认true | true |

### 字段映射说明

`fieldMapping` 参数是一个键值对映射：
- **Key（源字段）**：包含图片的字段名称
- **Value（目标字段）**：要写入本地URL的字段名称

```json
{
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url",
    "👚上装背面图": "👚上装背面图url",
    "👖下装正面图": "👖下装正面图url",
    "产品主图": "产品主图url"
  }
}
```

## 📝 使用示例

### 示例1: 基本字段映射

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "updateBitableWithLocalUrl": true
  }'
```

**处理流程**：
1. 从 `👚上装正面图` 字段获取图片
2. 下载图片到本地服务器
3. 生成本地访问URL
4. 将本地URL写入 `👚上装正面图url` 字段

### 示例2: 多字段批量处理

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url",
      "👖下装正面图": "👖下装正面图url",
      "👖下装背面图": "👖下装背面图url",
      "👠鞋子图片": "👠鞋子图片url"
    },
    "pageSize": 10,
    "maxConcurrentDownloads": 3,
    "downloadTimeout": 60
  }'
```

### 示例3: 指定记录处理

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "fieldMapping": {
      "产品主图": "产品主图url",
      "产品详情图": "产品详情图url"
    }
  }'
```

### 示例4: 使用筛选条件

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "filter": "CurrentValue.[状态] = \"待处理\"",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    }
  }'
```

## 📊 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalRecords": 2,
    "totalImages": 4,
    "successfulDownloads": 4,
    "failedDownloads": 0,
    "records": [
      {
        "recordId": "recqwIwhc6",
        "imageFields": {
          "👚上装正面图": [
            {
              "fileToken": "boxcnxe8OIukXXXXXXXXXXXXXX",
              "originalName": "front.jpg",
              "localAccessUrl": "http://localhost:8080/uploads/2025/07/28/front_123456.jpg",
              "downloadStatus": "SUCCESS"
            }
          ]
        }
      }
    ],
    "hasMore": false,
    "processingTime": 2500
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "字段映射关系不能为空",
  "success": false
}
```

## 🔧 技术实现

### 处理流程

```mermaid
graph TD
    A[接收请求] --> B[验证参数]
    B --> C[获取多维表格记录]
    C --> D[遍历字段映射关系]
    D --> E[识别源字段图片]
    E --> F[下载图片到本地]
    F --> G[生成本地URL]
    G --> H[写入目标字段]
    H --> I[返回处理结果]
```

### 字段类型支持

1. **飞书附件字段**：包含 `file_token`、`name`、`url` 等属性的对象数组
2. **URL字符串字段**：直接包含图片URL的字符串字段

### 本地存储

- **存储路径**：`uploads/YYYY/MM/DD/`
- **文件命名**：`原文件名_时间戳_随机数.扩展名`
- **访问URL**：`http://域名/uploads/YYYY/MM/DD/文件名`

## ⚠️ 注意事项

1. **权限要求**：确保飞书应用有读写多维表格的权限
2. **字段存在性**：目标字段必须在多维表格中存在
3. **字段类型**：目标字段应为文本类型以存储URL
4. **网络连接**：确保服务器能访问飞书API和图片URL
5. **存储空间**：确保服务器有足够的存储空间
6. **并发控制**：合理设置并发下载数，避免服务器过载

## 🎯 使用场景

### 1. 服装电商图片管理
```json
{
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url",
    "👚上装背面图": "👚上装背面图url",
    "👖下装正面图": "👖下装正面图url",
    "👖下装背面图": "👖下装背面图url"
  }
}
```

### 2. 产品图片本地化
```json
{
  "fieldMapping": {
    "产品主图": "产品主图url",
    "产品详情图1": "产品详情图1url",
    "产品详情图2": "产品详情图2url"
  }
}
```

### 3. 用户头像处理
```json
{
  "fieldMapping": {
    "用户头像": "用户头像url",
    "用户背景图": "用户背景图url"
  }
}
```

## 🔗 相关链接

- [测试脚本](./test-field-mapping-upload.sh)
- [飞书多维表格API文档](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN)
- [Swagger API文档](http://localhost:8080/swagger-ui.html)

## 🚀 快速开始

1. **运行测试脚本**：
   ```bash
   chmod +x test-field-mapping-upload.sh
   ./test-field-mapping-upload.sh
   ```

2. **查看处理日志**：
   ```bash
   tail -f logs/ziniao-api.log | grep -E "(字段映射|field-mapping)"
   ```

3. **验证结果**：
   - 检查多维表格中目标字段是否已更新
   - 访问本地URL确认图片可正常访问
   - 查看 `uploads/` 目录确认图片已下载
