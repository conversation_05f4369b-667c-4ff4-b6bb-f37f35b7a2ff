package com.ziniao;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;

/**
 * 紫鸟AI穿衣Demo应用主类（简化版，不包含Swagger）
 */
@SpringBootApplication(exclude = {
    // 排除可能导致问题的自动配置
})
@EnableConfigurationProperties
@EnableCaching
public class ZiniaoAiDemoSimpleApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZiniaoAiDemoSimpleApplication.class, args);
        System.out.println("=== 紫鸟AI穿衣Demo应用启动成功 ===");
        System.out.println("应用地址: http://localhost:8080");
        System.out.println("API测试:");
        System.out.println("  获取令牌: curl -X GET http://localhost:8080/api/token/app");
        System.out.println("  AI穿衣: curl -X POST http://localhost:8080/api/clothing/process -H 'Content-Type: application/json' -d '{...}'");
    }
}
