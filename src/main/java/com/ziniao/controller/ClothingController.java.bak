package com.ziniao.controller;

import com.ziniao.model.ClothingRequest;
import com.ziniao.model.ClothingResponse;
import com.ziniao.service.ClothingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * AI穿衣控制器
 */
@Api(tags = "AI穿衣接口")
@RestController
@RequestMapping("/api/clothing")
@Validated
public class ClothingController {
    
    private static final Logger logger = LoggerFactory.getLogger(ClothingController.class);
    
    @Autowired
    private ClothingService clothingService;
    
    /**
     * AI穿衣处理
     */
    @ApiOperation(value = "AI穿衣处理", notes = "上传人物图片和服装图片，生成穿衣效果")
    @PostMapping("/process")
    public ResponseEntity<ClothingResponse> processClothing(
            @ApiParam(value = "穿衣请求参数", required = true)
            @Valid @RequestBody ClothingRequest request) {
        
        try {
            logger.info("收到AI穿衣请求: {}", request);
            
            ClothingResponse response = clothingService.processClothing(request);
            
            if (response.isSuccess()) {
                logger.info("AI穿衣处理成功");
                return ResponseEntity.ok(response);
            } else {
                logger.error("AI穿衣处理失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("AI穿衣处理异常", e);
            
            ClothingResponse errorResponse = new ClothingResponse();
            errorResponse.setCode(500);
            errorResponse.setMessage("服务器内部错误: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 查询任务状态
     */
    @ApiOperation(value = "查询任务状态", notes = "根据任务ID查询处理状态")
    @GetMapping("/task/{taskId}")
    public ResponseEntity<ClothingResponse> queryTaskStatus(
            @ApiParam(value = "任务ID", required = true, example = "task_123456")
            @PathVariable @NotBlank String taskId) {
        
        try {
            logger.info("查询任务状态, taskId: {}", taskId);
            
            ClothingResponse response = clothingService.queryTaskStatus(taskId);
            
            if (response.isSuccess()) {
                logger.info("任务状态查询成功");
                return ResponseEntity.ok(response);
            } else {
                logger.error("任务状态查询失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("任务状态查询异常", e);
            
            ClothingResponse errorResponse = new ClothingResponse();
            errorResponse.setCode(500);
            errorResponse.setMessage("服务器内部错误: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 等待任务完成
     */
    @ApiOperation(value = "等待任务完成", notes = "轮询等待任务完成，返回最终结果")
    @PostMapping("/task/{taskId}/wait")
    public ResponseEntity<ClothingResponse> waitForTaskCompletion(
            @ApiParam(value = "任务ID", required = true, example = "task_123456")
            @PathVariable @NotBlank String taskId,
            
            @ApiParam(value = "最大等待时间（秒）", example = "300")
            @RequestParam(defaultValue = "300") int maxWaitSeconds,
            
            @ApiParam(value = "轮询间隔（秒）", example = "5")
            @RequestParam(defaultValue = "5") int pollIntervalSeconds) {
        
        try {
            logger.info("等待任务完成, taskId: {}, maxWaitSeconds: {}, pollIntervalSeconds: {}", 
                       taskId, maxWaitSeconds, pollIntervalSeconds);
            
            long maxWaitTime = maxWaitSeconds * 1000L;
            long pollInterval = pollIntervalSeconds * 1000L;
            
            ClothingResponse response = clothingService.waitForTaskCompletion(taskId, maxWaitTime, pollInterval);
            
            if (response.isSuccess()) {
                logger.info("任务等待完成");
                return ResponseEntity.ok(response);
            } else {
                logger.error("任务等待失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("任务等待异常", e);
            
            ClothingResponse errorResponse = new ClothingResponse();
            errorResponse.setCode(500);
            errorResponse.setMessage("服务器内部错误: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
