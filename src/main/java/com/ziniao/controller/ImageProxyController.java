package com.ziniao.controller;

import com.ziniao.service.ImageProxyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 图片代理访问控制器
 * 提供安全的图片访问方式，隐藏真实文件路径
 */
@Api(tags = "图片代理访问服务")
@RestController
@RequestMapping("/api/image-proxy")
public class ImageProxyController {

    private static final Logger logger = LoggerFactory.getLogger(ImageProxyController.class);

    @Autowired
    private ImageProxyService imageProxyService;

    /**
     * 通过加密路径访问图片
     */
    @ApiOperation(value = "通过加密路径访问图片", notes = "使用加密的路径参数访问图片，隐藏真实文件路径")
    @GetMapping("/view/{encodedPath}")
    public ResponseEntity<Resource> viewImageByEncodedPath(
            @ApiParam(value = "加密的图片路径", required = true)
            @PathVariable String encodedPath,
            HttpServletRequest request) {

        try {
            logger.info("收到图片代理访问请求，加密路径: {}", encodedPath);

            // 通过代理服务获取图片资源
            Resource resource = imageProxyService.getImageByEncodedPath(encodedPath);
            
            if (resource == null || !resource.exists()) {
                logger.warn("图片不存在或无法访问: {}", encodedPath);
                return ResponseEntity.notFound().build();
            }

            // 获取文件的媒体类型
            String contentType = imageProxyService.getContentType(resource);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            logger.info("成功获取图片资源: {}, 类型: {}", resource.getFilename(), contentType);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);

        } catch (Exception e) {
            logger.error("图片代理访问失败: " + encodedPath, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 通过图片ID访问图片
     */
    @ApiOperation(value = "通过图片ID访问图片", notes = "使用图片ID访问图片，更加安全和简洁")
    @GetMapping("/id/{imageId}")
    public ResponseEntity<Resource> viewImageById(
            @ApiParam(value = "图片ID", required = true)
            @PathVariable String imageId,
            HttpServletRequest request) {

        try {
            logger.info("收到图片ID访问请求: {}", imageId);

            // 通过代理服务获取图片资源
            Resource resource = imageProxyService.getImageById(imageId);
            
            if (resource == null || !resource.exists()) {
                logger.warn("图片不存在或无法访问，ID: {}", imageId);
                return ResponseEntity.notFound().build();
            }

            // 获取文件的媒体类型
            String contentType = imageProxyService.getContentType(resource);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            logger.info("成功获取图片资源: {}, 类型: {}", resource.getFilename(), contentType);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);

        } catch (Exception e) {
            logger.error("图片ID访问失败: " + imageId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取图片信息（不返回图片内容）
     */
    @ApiOperation(value = "获取图片信息", notes = "获取图片的基本信息，如大小、类型等")
    @GetMapping("/info/{encodedPath}")
    public ResponseEntity<?> getImageInfo(
            @ApiParam(value = "加密的图片路径", required = true)
            @PathVariable String encodedPath) {

        try {
            logger.info("收到图片信息查询请求: {}", encodedPath);

            // 通过代理服务获取图片信息
            Object imageInfo = imageProxyService.getImageInfo(encodedPath);
            
            if (imageInfo == null) {
                logger.warn("图片信息不存在: {}", encodedPath);
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(imageInfo);

        } catch (Exception e) {
            logger.error("获取图片信息失败: " + encodedPath, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成图片访问URL
     */
    @ApiOperation(value = "生成图片访问URL", notes = "为指定的文件路径生成代理访问URL")
    @PostMapping("/generate-url")
    public ResponseEntity<?> generateImageUrl(
            @ApiParam(value = "原始文件路径", required = true)
            @RequestParam String filePath,
            HttpServletRequest request) {

        try {
            logger.info("收到生成图片URL请求: {}", filePath);

            // 生成代理访问URL
            String proxyUrl = imageProxyService.generateProxyUrl(filePath, request);
            
            if (proxyUrl == null) {
                logger.warn("无法生成代理URL: {}", filePath);
                return ResponseEntity.badRequest().body("无法生成代理URL");
            }

            logger.info("成功生成代理URL: {} -> {}", filePath, proxyUrl);
            return ResponseEntity.ok().body(new ProxyUrlResponse(filePath, proxyUrl));

        } catch (Exception e) {
            logger.error("生成图片URL失败: " + filePath, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 代理URL响应类
     */
    public static class ProxyUrlResponse {
        private String originalPath;
        private String proxyUrl;

        public ProxyUrlResponse(String originalPath, String proxyUrl) {
            this.originalPath = originalPath;
            this.proxyUrl = proxyUrl;
        }

        public String getOriginalPath() {
            return originalPath;
        }

        public void setOriginalPath(String originalPath) {
            this.originalPath = originalPath;
        }

        public String getProxyUrl() {
            return proxyUrl;
        }

        public void setProxyUrl(String proxyUrl) {
            this.proxyUrl = proxyUrl;
        }
    }
}
