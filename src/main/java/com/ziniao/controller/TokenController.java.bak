package com.ziniao.controller;

import com.ziniao.model.TokenResponse;
import com.ziniao.service.TokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 令牌管理控制器
 */
@Api(tags = "令牌管理接口")
@RestController
@RequestMapping("/api/token")
public class TokenController {
    
    private static final Logger logger = LoggerFactory.getLogger(TokenController.class);
    
    @Autowired
    private TokenService tokenService;
    
    /**
     * 获取应用令牌
     */
    @ApiOperation(value = "获取应用令牌", notes = "获取用于API调用的应用令牌")
    @GetMapping("/app")
    public ResponseEntity<Map<String, Object>> getAppToken() {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("获取应用令牌请求");
            
            String token = tokenService.getAppToken();
            
            if (tokenService.validateToken(token)) {
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", new TokenResponse(token, null));
                
                logger.info("应用令牌获取成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("code", 400);
                result.put("message", "获取的令牌无效");
                
                logger.error("获取的应用令牌无效");
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            logger.error("获取应用令牌异常", e);
            
            result.put("code", 500);
            result.put("message", "服务器内部错误: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 刷新应用令牌
     */
    @ApiOperation(value = "刷新应用令牌", notes = "清除缓存并重新获取应用令牌")
    @PostMapping("/app/refresh")
    public ResponseEntity<Map<String, Object>> refreshAppToken() {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("刷新应用令牌请求");
            
            String token = tokenService.refreshToken();
            
            if (tokenService.validateToken(token)) {
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", new TokenResponse(token, null));
                
                logger.info("应用令牌刷新成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("code", 400);
                result.put("message", "刷新的令牌无效");
                
                logger.error("刷新的应用令牌无效");
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            logger.error("刷新应用令牌异常", e);
            
            result.put("code", 500);
            result.put("message", "服务器内部错误: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
