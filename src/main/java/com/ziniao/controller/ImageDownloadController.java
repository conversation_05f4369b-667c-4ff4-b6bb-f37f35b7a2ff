package com.ziniao.controller;

import com.ziniao.model.FileUploadResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 图片URL下载控制器
 */
@Api(tags = "图片URL下载服务")
@RestController
@RequestMapping("/api/image")
public class ImageDownloadController {

    private static final Logger logger = LoggerFactory.getLogger(ImageDownloadController.class);

    @Value("${file.upload.path:uploads/}")
    private String uploadPath;

    @Value("${file.upload.max-size:209715200}")
    private long maxFileSize;

    @Value("${file.upload.url-prefix:/uploads/}")
    private String urlPrefix;

    /**
     * 从URL下载图片并保存到服务器
     */
    @ApiOperation(value = "从URL下载图片", notes = "传入图片URL，下载图片并保存到服务器，返回服务器上的访问URL")
    @PostMapping("/download-from-url")
    public ResponseEntity<FileUploadResponse> downloadImageFromUrl(
            @ApiParam(value = "图片URL地址", required = true, example = "https://httpbin.org/image/jpeg")
            @RequestParam("imageUrl") String imageUrl,
            HttpServletRequest request) {

        try {
            logger.info("收到图片URL下载请求: {}", imageUrl);

            // 1. 验证URL
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(FileUploadResponse.error(400, "图片URL不能为空"));
            }

            // 2. 创建URL连接
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            // 3. 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                return ResponseEntity.badRequest()
                        .body(FileUploadResponse.error(400, "无法访问图片URL，响应状态: " + responseCode));
            }

            // 4. 获取内容类型和文件大小
            String contentType = connection.getContentType();
            long contentLength = connection.getContentLengthLong();

            logger.info("图片信息 - Content-Type: {}, Content-Length: {}", contentType, contentLength);

            // 5. 验证内容类型
            if (contentType == null || !contentType.startsWith("image/")) {
                return ResponseEntity.badRequest()
                        .body(FileUploadResponse.error(400, "URL指向的不是图片文件，Content-Type: " + contentType));
            }

            // 6. 检查文件大小
            if (contentLength > maxFileSize) {
                return ResponseEntity.badRequest()
                        .body(FileUploadResponse.error(400, 
                                String.format("图片文件过大，最大允许 %d MB", maxFileSize / 1024 / 1024)));
            }

            // 7. 从URL中提取文件扩展名，如果没有则根据Content-Type推断
            String fileExtension = extractExtensionFromUrl(imageUrl, contentType);
            
            // 8. 生成文件名和路径
            String originalFileName = extractFileNameFromUrl(imageUrl);
            String newFileName = generateFileName(originalFileName, fileExtension);
            
            // 9. 创建按日期分组的目录结构
            String dateFolder = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String relativePath = urlPrefix + dateFolder + "/" + newFileName;
            String fullPath = uploadPath + dateFolder + "/" + newFileName;

            // 10. 确保目录存在
            Path targetPath = Paths.get(fullPath);
            Files.createDirectories(targetPath.getParent());

            // 11. 下载并保存文件
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(targetPath.toFile())) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytesRead = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }
                
                logger.info("图片下载完成，总大小: {} bytes", totalBytesRead);
            }

            // 12. 生成完整的访问URL
            String fileUrl = buildFileUrl(request, relativePath);

            // 13. 创建文件信息
            FileUploadResponse.FileInfo fileInfo = new FileUploadResponse.FileInfo(
                    originalFileName,
                    newFileName,
                    Files.size(targetPath),
                    contentType,
                    fileUrl,
                    relativePath,
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
            );

            logger.info("图片从URL保存成功: {}", fileInfo);
            return ResponseEntity.ok(FileUploadResponse.success(fileInfo));

        } catch (IOException e) {
            logger.error("从URL下载图片失败: " + imageUrl, e);
            return ResponseEntity.internalServerError()
                    .body(FileUploadResponse.error("图片下载失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("从URL下载图片异常: " + imageUrl, e);
            return ResponseEntity.internalServerError()
                    .body(FileUploadResponse.error("图片下载异常: " + e.getMessage()));
        }
    }

    /**
     * 从URL中提取文件扩展名
     */
    private String extractExtensionFromUrl(String imageUrl, String contentType) {
        // 首先尝试从URL中提取扩展名
        String extension = "";
        if (imageUrl.contains(".")) {
            String urlPath = imageUrl.split("\\?")[0]; // 移除查询参数
            int lastDotIndex = urlPath.lastIndexOf(".");
            if (lastDotIndex != -1 && lastDotIndex < urlPath.length() - 1) {
                extension = urlPath.substring(lastDotIndex + 1).toLowerCase();
            }
        }
        
        // 如果从URL中无法提取扩展名，则根据Content-Type推断
        if (extension.isEmpty() && contentType != null) {
            switch (contentType.toLowerCase()) {
                case "image/jpeg":
                    extension = "jpg";
                    break;
                case "image/png":
                    extension = "png";
                    break;
                case "image/gif":
                    extension = "gif";
                    break;
                case "image/webp":
                    extension = "webp";
                    break;
                case "image/bmp":
                    extension = "bmp";
                    break;
                default:
                    extension = "jpg"; // 默认为jpg
                    break;
            }
        }
        
        return extension.isEmpty() ? "jpg" : extension;
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String imageUrl) {
        try {
            String urlPath = imageUrl.split("\\?")[0]; // 移除查询参数
            int lastSlashIndex = urlPath.lastIndexOf("/");
            if (lastSlashIndex != -1 && lastSlashIndex < urlPath.length() - 1) {
                return urlPath.substring(lastSlashIndex + 1);
            }
        } catch (Exception e) {
            logger.warn("无法从URL提取文件名: " + imageUrl, e);
        }
        return "downloaded_image";
    }

    /**
     * 生成新的文件名
     */
    private String generateFileName(String originalFileName, String extension) {
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return timestamp + "_" + uuid + "." + extension;
    }

    /**
     * 构建文件访问URL
     */
    private String buildFileUrl(HttpServletRequest request, String relativePath) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int port = request.getServerPort();
        String contextPath = request.getContextPath();

        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);
        
        // 只有在非标准端口时才添加端口号
        if ((!"http".equals(scheme) || port != 80) && (!"https".equals(scheme) || port != 443)) {
            url.append(":").append(port);
        }
        
        url.append(contextPath);
        
        // 确保relativePath以/开头
        if (!relativePath.startsWith("/")) {
            url.append("/");
        }
        url.append(relativePath);

        return url.toString();
    }
}
