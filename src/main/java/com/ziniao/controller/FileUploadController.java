package com.ziniao.controller;

import com.ziniao.model.FileUploadResponse;
import com.ziniao.model.ImageUrlRequest;
import com.ziniao.service.FileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件上传控制器
 */
@Api(tags = "文件上传服务")
@RestController
@RequestMapping("/api/file")
public class FileUploadController {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private FileUploadService fileUploadService;

    /**
     * 单文件上传
     */
    @ApiOperation(value = "单文件上传", notes = "上传单个文件，支持最大200MB，返回文件的完整访问URL")
    @PostMapping("/upload")
    public ResponseEntity<FileUploadResponse> uploadFile(
            @ApiParam(value = "上传的文件", required = true)
            @RequestParam("file") MultipartFile file,
            HttpServletRequest request) {

        try {
            logger.info("收到文件上传请求: {}, 大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());

            FileUploadResponse response = fileUploadService.uploadFile(file, request);

            if (response.isSuccess()) {
                logger.info("文件上传成功: {}", response.getData().getFileUrl());
                return ResponseEntity.ok(response);
            } else {
                logger.error("文件上传失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("文件上传异常", e);
            FileUploadResponse errorResponse = FileUploadResponse.error("文件上传异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 多文件上传
     */
    @ApiOperation(value = "多文件上传", notes = "批量上传多个文件，每个文件最大200MB")
    @PostMapping("/upload/batch")
    public ResponseEntity<FileUploadResponse[]> uploadFiles(
            @ApiParam(value = "上传的文件数组", required = true)
            @RequestParam("files") MultipartFile[] files,
            HttpServletRequest request) {

        try {
            logger.info("收到批量文件上传请求，文件数量: {}", files.length);

            if (files.length == 0) {
                FileUploadResponse errorResponse = FileUploadResponse.error(400, "请选择要上传的文件");
                return ResponseEntity.badRequest().body(new FileUploadResponse[]{errorResponse});
            }

            FileUploadResponse[] responses = new FileUploadResponse[files.length];
            boolean hasError = false;

            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                logger.info("处理第{}个文件: {}", i + 1, file.getOriginalFilename());
                
                responses[i] = fileUploadService.uploadFile(file, request);
                
                if (!responses[i].isSuccess()) {
                    hasError = true;
                    logger.error("第{}个文件上传失败: {}", i + 1, responses[i].getMessage());
                } else {
                    logger.info("第{}个文件上传成功: {}", i + 1, responses[i].getData().getFileUrl());
                }
            }

            if (hasError) {
                logger.warn("批量上传完成，但存在失败的文件");
                return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT).body(responses);
            } else {
                logger.info("批量上传全部成功");
                return ResponseEntity.ok(responses);
            }

        } catch (Exception e) {
            logger.error("批量文件上传异常", e);
            FileUploadResponse errorResponse = FileUploadResponse.error("批量文件上传异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(new FileUploadResponse[]{errorResponse});
        }
    }

    /**
     * 文件下载
     */
    @ApiOperation(value = "文件下载", notes = "根据文件路径下载文件")
    @GetMapping("/download/**")
    public ResponseEntity<Resource> downloadFile(HttpServletRequest request) {
        try {
            // 获取文件路径（去掉/api/file/download前缀）
            String requestURI = request.getRequestURI();
            String filePath = requestURI.substring("/api/file/download".length());
            
            logger.info("收到文件下载请求: {}", filePath);

            File file = fileUploadService.getFile(filePath);
            if (file == null || !file.exists()) {
                logger.error("文件不存在: {}", filePath);
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);
            String fileName = file.getName();

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename*=UTF-8''" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));

            logger.info("文件下载成功: {}", filePath);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } catch (Exception e) {
            logger.error("文件下载异常", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除文件
     */
    @ApiOperation(value = "删除文件", notes = "根据文件路径删除文件")
    @DeleteMapping("/delete")
    public ResponseEntity<FileUploadResponse> deleteFile(
            @ApiParam(value = "文件相对路径", required = true, example = "/uploads/2025/07/20/20250720_123456_abc123.jpg")
            @RequestParam("filePath") String filePath) {

        try {
            logger.info("收到文件删除请求: {}", filePath);

            boolean deleted = fileUploadService.deleteFile(filePath);
            
            if (deleted) {
                logger.info("文件删除成功: {}", filePath);
                return ResponseEntity.ok(new FileUploadResponse(200, "文件删除成功", true));
            } else {
                logger.error("文件删除失败: {}", filePath);
                return ResponseEntity.badRequest()
                        .body(FileUploadResponse.error(400, "文件删除失败，文件可能不存在"));
            }

        } catch (Exception e) {
            logger.error("文件删除异常", e);
            return ResponseEntity.internalServerError()
                    .body(FileUploadResponse.error("文件删除异常: " + e.getMessage()));
        }
    }

    /**
     * 检查文件是否存在
     */
    @ApiOperation(value = "检查文件存在性", notes = "检查指定路径的文件是否存在")
    @GetMapping("/exists")
    public ResponseEntity<FileUploadResponse> checkFileExists(
            @ApiParam(value = "文件相对路径", required = true, example = "/uploads/2025/07/20/20250720_123456_abc123.jpg")
            @RequestParam("filePath") String filePath) {

        try {
            logger.info("检查文件存在性: {}", filePath);

            boolean exists = fileUploadService.fileExists(filePath);
            
            FileUploadResponse response = new FileUploadResponse(
                    200, 
                    exists ? "文件存在" : "文件不存在", 
                    true
            );

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("检查文件存在性异常", e);
            return ResponseEntity.internalServerError()
                    .body(FileUploadResponse.error("检查文件存在性异常: " + e.getMessage()));
        }
    }

    /**
     * 从URL下载图片并保存到服务器（表单参数方式）
     */
    @ApiOperation(value = "从URL下载图片（表单参数）", notes = "传入图片URL，下载图片并保存到服务器，返回服务器上的访问URL")
    @PostMapping("/download-from-url")
    public ResponseEntity<FileUploadResponse> downloadImageFromUrl(
            @ApiParam(value = "图片URL地址", required = true, example = "https://example.com/image.jpg")
            @RequestParam("imageUrl") String imageUrl,
            HttpServletRequest request) {

        try {
            logger.info("收到图片URL下载请求（表单参数）: {}", imageUrl);

            FileUploadResponse response = fileUploadService.downloadImageFromUrl(imageUrl, request);

            if (response.isSuccess()) {
                logger.info("图片URL下载成功: {}", response.getData().getFileUrl());
                return ResponseEntity.ok(response);
            } else {
                logger.error("图片URL下载失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("图片URL下载异常", e);
            FileUploadResponse errorResponse = FileUploadResponse.error("图片URL下载异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 从URL下载图片并保存到服务器（JSON格式）
     */
    @ApiOperation(value = "从URL下载图片（JSON格式）", notes = "传入JSON格式的图片URL请求，下载图片并保存到服务器")
    @PostMapping("/download-image")
    public ResponseEntity<FileUploadResponse> downloadImage(
            @ApiParam(value = "图片URL请求参数", required = true)
            @Valid @RequestBody ImageUrlRequest request,
            HttpServletRequest httpRequest) {

        try {
            logger.info("收到图片URL下载请求（JSON格式）: {}", request);

            FileUploadResponse response = fileUploadService.downloadImageFromUrl(request.getImageUrl(), httpRequest);

            if (response.isSuccess()) {
                logger.info("图片URL下载成功: {}", response.getData().getFileUrl());
                return ResponseEntity.ok(response);
            } else {
                logger.error("图片URL下载失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("图片URL下载异常", e);
            FileUploadResponse errorResponse = FileUploadResponse.error("图片URL下载异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 文件上传服务健康检查
     */
    @ApiOperation(value = "健康检查", notes = "检查文件上传服务是否正常运行")
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("文件上传服务运行正常");
    }
}



