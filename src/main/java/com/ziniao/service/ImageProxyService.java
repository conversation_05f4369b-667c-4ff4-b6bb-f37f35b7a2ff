package com.ziniao.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 图片代理访问服务
 * 提供安全的图片访问方式，隐藏真实文件路径
 */
@Service
public class ImageProxyService {

    private static final Logger logger = LoggerFactory.getLogger(ImageProxyService.class);

    @Value("${file.upload.path:uploads/}")
    private String uploadPath;

    @Value("${file.upload.url-prefix:/uploads/}")
    private String urlPrefix;

    @Value("${file.upload.server-base-url:http://localhost:8080}")
    private String serverBaseUrl;

    @Value("${image.proxy.secret-key:ziniao-image-proxy-2025}")
    private String secretKey;

    @Value("${image.proxy.enable-id-mapping:true}")
    private boolean enableIdMapping;

    // 图片ID到路径的映射缓存
    private final Map<String, String> imageIdToPathMap = new ConcurrentHashMap<>();
    private final Map<String, String> pathToImageIdMap = new ConcurrentHashMap<>();

    /**
     * 通过加密路径获取图片资源
     */
    public Resource getImageByEncodedPath(String encodedPath) {
        try {
            // 解密路径
            String decodedPath = decodeImagePath(encodedPath);
            if (decodedPath == null) {
                logger.warn("无法解密图片路径: {}", encodedPath);
                return null;
            }

            logger.debug("解密后的路径: {}", decodedPath);

            // 构建完整的文件路径
            String fullPath = buildFullPath(decodedPath);
            File file = new File(fullPath);

            if (!file.exists() || !file.isFile()) {
                logger.warn("图片文件不存在: {}", fullPath);
                return null;
            }

            // 安全检查：确保文件在允许的目录内
            if (!isPathSafe(fullPath)) {
                logger.warn("不安全的文件路径访问尝试: {}", fullPath);
                return null;
            }

            return new FileSystemResource(file);

        } catch (Exception e) {
            logger.error("获取图片资源失败: " + encodedPath, e);
            return null;
        }
    }

    /**
     * 通过图片ID获取图片资源
     */
    public Resource getImageById(String imageId) {
        try {
            logger.info("=== 通过图片ID获取资源开始 ===");
            logger.info("请求的图片ID: {}", imageId);

            // 从映射中获取路径
            String imagePath = imageIdToPathMap.get(imageId);
            if (imagePath == null) {
                logger.warn("图片ID不存在: {}", imageId);
                logger.info("当前映射表大小: {}", imageIdToPathMap.size());
                logger.info("映射表内容: {}", imageIdToPathMap);
                return null;
            }

            logger.info("图片ID {} 对应路径: {}", imageId, imagePath);

            // 构建完整的文件路径
            String fullPath = buildFullPath(imagePath);
            File file = new File(fullPath);

            logger.info("检查文件是否存在: {}", fullPath);
            logger.info("文件存在: {}, 是文件: {}", file.exists(), file.isFile());

            if (!file.exists() || !file.isFile()) {
                logger.warn("图片文件不存在: {}", fullPath);
                // 清理无效的映射
                imageIdToPathMap.remove(imageId);
                pathToImageIdMap.remove(imagePath);
                logger.info("已清理无效映射: {}", imageId);
                return null;
            }

            logger.info("成功找到图片文件: {}", fullPath);
            logger.info("=== 通过图片ID获取资源结束 ===");
            return new FileSystemResource(file);

        } catch (Exception e) {
            logger.error("通过ID获取图片资源失败: " + imageId, e);
            return null;
        }
    }

    /**
     * 获取图片的内容类型
     */
    public String getContentType(Resource resource) {
        try {
            String filename = resource.getFilename();
            if (filename == null) {
                return null;
            }

            // 首先尝试通过文件扩展名判断
            String contentType = URLConnection.guessContentTypeFromName(filename);
            if (contentType != null) {
                return contentType;
            }

            // 如果无法通过文件名判断，尝试通过文件内容判断
            try {
                Path path = Paths.get(resource.getURI());
                contentType = Files.probeContentType(path);
                if (contentType != null) {
                    return contentType;
                }
            } catch (Exception e) {
                logger.debug("无法通过文件内容判断类型: {}", e.getMessage());
            }

            // 根据文件扩展名手动判断
            String extension = getFileExtension(filename).toLowerCase();
            switch (extension) {
                case "jpg":
                case "jpeg":
                    return "image/jpeg";
                case "png":
                    return "image/png";
                case "gif":
                    return "image/gif";
                case "webp":
                    return "image/webp";
                case "bmp":
                    return "image/bmp";
                case "svg":
                    return "image/svg+xml";
                default:
                    return "application/octet-stream";
            }

        } catch (Exception e) {
            logger.error("获取内容类型失败", e);
            return "application/octet-stream";
        }
    }

    /**
     * 获取图片信息
     */
    public Object getImageInfo(String encodedPath) {
        try {
            Resource resource = getImageByEncodedPath(encodedPath);
            if (resource == null || !resource.exists()) {
                return null;
            }

            Map<String, Object> info = new HashMap<>();
            info.put("filename", resource.getFilename());
            info.put("size", resource.contentLength());
            info.put("contentType", getContentType(resource));
            info.put("lastModified", resource.lastModified());

            return info;

        } catch (Exception e) {
            logger.error("获取图片信息失败: " + encodedPath, e);
            return null;
        }
    }

    /**
     * 生成代理访问URL
     */
    public String generateProxyUrl(String filePath, HttpServletRequest request) {
        try {
            // 标准化文件路径
            String normalizedPath = normalizeFilePath(filePath);
            
            if (enableIdMapping) {
                // 使用ID映射方式
                String imageId = getOrCreateImageId(normalizedPath);
                return buildProxyUrl(request, "/api/image-proxy/id/" + imageId);
            } else {
                // 使用加密路径方式
                String encodedPath = encodeImagePath(normalizedPath);
                return buildProxyUrl(request, "/api/image-proxy/view/" + encodedPath);
            }

        } catch (Exception e) {
            logger.error("生成代理URL失败: " + filePath, e);
            return null;
        }
    }

    /**
     * 加密图片路径
     */
    private String encodeImagePath(String imagePath) {
        try {
            // 使用Base64编码 + 简单的混淆
            String data = imagePath + "|" + System.currentTimeMillis();
            String encoded = Base64.getUrlEncoder().encodeToString(data.getBytes("UTF-8"));
            
            // 添加简单的校验和
            String checksum = generateChecksum(imagePath);
            return encoded + "." + checksum;

        } catch (Exception e) {
            logger.error("加密图片路径失败: " + imagePath, e);
            return null;
        }
    }

    /**
     * 解密图片路径
     */
    private String decodeImagePath(String encodedPath) {
        try {
            // 分离编码数据和校验和
            int dotIndex = encodedPath.lastIndexOf('.');
            if (dotIndex == -1) {
                return null;
            }

            String encoded = encodedPath.substring(0, dotIndex);
            String checksum = encodedPath.substring(dotIndex + 1);

            // 解码
            byte[] decodedBytes = Base64.getUrlDecoder().decode(encoded);
            String decodedData = new String(decodedBytes, "UTF-8");

            // 提取路径（忽略时间戳）
            int pipeIndex = decodedData.indexOf('|');
            if (pipeIndex == -1) {
                return null;
            }

            String imagePath = decodedData.substring(0, pipeIndex);

            // 验证校验和
            String expectedChecksum = generateChecksum(imagePath);
            if (!checksum.equals(expectedChecksum)) {
                logger.warn("图片路径校验和不匹配");
                return null;
            }

            return imagePath;

        } catch (Exception e) {
            logger.error("解密图片路径失败: " + encodedPath, e);
            return null;
        }
    }

    /**
     * 生成或获取图片ID
     */
    private String getOrCreateImageId(String imagePath) {
        // 先检查是否已存在映射
        String existingId = pathToImageIdMap.get(imagePath);
        if (existingId != null) {
            return existingId;
        }

        // 生成新的ID
        String imageId = generateImageId(imagePath);
        
        // 保存映射关系
        imageIdToPathMap.put(imageId, imagePath);
        pathToImageIdMap.put(imagePath, imageId);

        logger.debug("创建新的图片ID映射: {} -> {}", imageId, imagePath);
        return imageId;
    }

    /**
     * 生成图片ID
     */
    private String generateImageId(String imagePath) {
        try {
            // 使用MD5生成唯一ID
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update((imagePath + secretKey).getBytes("UTF-8"));
            byte[] digest = md.digest();

            // 转换为16进制字符串，取前12位
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < Math.min(6, digest.length); i++) {
                sb.append(String.format("%02x", digest[i]));
            }

            return sb.toString();

        } catch (NoSuchAlgorithmException | IOException e) {
            logger.error("生成图片ID失败", e);
            // 降级方案：使用简单的哈希
            return String.valueOf(Math.abs(imagePath.hashCode()));
        }
    }

    /**
     * 手动注册图片ID映射（供其他服务调用）
     */
    public void registerImageMapping(String imageId, String imagePath) {
        if (imageId != null && imagePath != null) {
            imageIdToPathMap.put(imageId, imagePath);
            pathToImageIdMap.put(imagePath, imageId);
            logger.debug("注册图片ID映射: {} -> {}", imageId, imagePath);
        }
    }

    /**
     * 获取图片ID（如果不存在则创建）
     */
    public String getImageId(String imagePath) {
        String normalizedPath = normalizeFilePath(imagePath);
        return getOrCreateImageId(normalizedPath);
    }

    /**
     * 生成校验和
     */
    private String generateChecksum(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update((data + secretKey).getBytes("UTF-8"));
            byte[] digest = md.digest();
            
            // 取前4位转换为16进制
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < Math.min(2, digest.length); i++) {
                sb.append(String.format("%02x", digest[i]));
            }
            
            return sb.toString();

        } catch (Exception e) {
            logger.error("生成校验和失败", e);
            return "0000";
        }
    }

    /**
     * 构建完整文件路径
     */
    private String buildFullPath(String relativePath) {
        logger.debug("=== 构建完整文件路径 ===");
        logger.debug("输入relativePath: {}", relativePath);
        logger.debug("uploadPath配置: {}", uploadPath);
        logger.debug("urlPrefix配置: {}", urlPrefix);

        // 移除开头的斜杠
        String cleanPath = relativePath;
        if (cleanPath.startsWith("/")) {
            cleanPath = cleanPath.substring(1);
            logger.debug("移除开头斜杠后: {}", cleanPath);
        }

        // 如果路径以urlPrefix开头，移除urlPrefix部分
        String urlPrefixWithoutSlash = urlPrefix.startsWith("/") ? urlPrefix.substring(1) : urlPrefix;
        if (cleanPath.startsWith(urlPrefixWithoutSlash)) {
            cleanPath = cleanPath.substring(urlPrefixWithoutSlash.length());
            logger.debug("移除urlPrefix后: {}", cleanPath);
        }

        // 确保uploadPath以/结尾
        String basePath = uploadPath;
        if (!basePath.endsWith("/")) {
            basePath += "/";
        }

        String fullPath = basePath + cleanPath;
        logger.debug("最终完整路径: {}", fullPath);
        logger.debug("=== 构建完整文件路径结束 ===");

        return fullPath;
    }

    /**
     * 标准化文件路径
     */
    private String normalizeFilePath(String filePath) {
        // 移除服务器基础URL
        if (filePath.startsWith(serverBaseUrl)) {
            filePath = filePath.substring(serverBaseUrl.length());
        }
        
        // 确保以/开头
        if (!filePath.startsWith("/")) {
            filePath = "/" + filePath;
        }

        return filePath;
    }

    /**
     * 构建代理URL
     */
    private String buildProxyUrl(HttpServletRequest request, String path) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int port = request.getServerPort();
        String contextPath = request.getContextPath();

        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);
        
        // 只有在非标准端口时才添加端口号
        if ((!"http".equals(scheme) || port != 80) && (!"https".equals(scheme) || port != 443)) {
            url.append(":").append(port);
        }
        
        url.append(contextPath).append(path);
        return url.toString();
    }

    /**
     * 检查路径是否安全
     */
    private boolean isPathSafe(String fullPath) {
        try {
            Path path = Paths.get(fullPath).toRealPath();
            Path uploadDir = Paths.get(uploadPath).toRealPath();
            
            return path.startsWith(uploadDir);

        } catch (IOException e) {
            logger.error("路径安全检查失败: " + fullPath, e);
            return false;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        
        return filename.substring(lastDotIndex + 1);
    }
}
