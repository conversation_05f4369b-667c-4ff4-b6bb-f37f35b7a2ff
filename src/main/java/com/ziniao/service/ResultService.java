package com.ziniao.service;

import com.alibaba.fastjson.JSON;
import com.fzzixun.openapi.sdk.client.OpenClient;
import com.fzzixun.openapi.sdk.common.RequestMethod;
import com.fzzixun.openapi.sdk.request.CommonRequest;
import com.fzzixun.openapi.sdk.response.CommonResponse;
import com.ziniao.config.ApiConfig;
import com.ziniao.model.ClothingResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 结果查询服务类
 */
@Service
public class ResultService {
    
    private static final Logger logger = LoggerFactory.getLogger(ResultService.class);
    
    @Autowired
    private ApiConfig apiConfig;
    
    @Autowired
    private TokenService tokenService;
    
    private OpenClient openClient;
    
    /**
     * 获取OpenClient实例（懒加载）
     */
    private OpenClient getOpenClient() {
        if (openClient == null) {
            openClient = new OpenClient(
                apiConfig.getBaseUrl(),
                apiConfig.getAppId(),
                apiConfig.getPrivateKey()
            );
        }
        return openClient;
    }
    
    /**
     * 查询AI穿衣结果
     * 根据官方文档：/linkfox-ai/image/v2/make/info
     *
     * @param taskId 任务ID
     * @return 查询结果
     * @throws Exception API调用异常
     */
    public ClothingResponse queryResult(String taskId) throws Exception {
        logger.info("查询AI穿衣结果, taskId: {}", taskId);

        // 获取应用令牌
        String appToken = tokenService.getAppToken();

        // 使用官方文档中的正确接口路径
        CommonRequest apiRequest = new CommonRequest("/linkfox-ai/image/v2/make/info", RequestMethod.POST_JSON);

        // 根据官方文档设置请求参数
        Map<String, Object> params = new HashMap<>();
        try {
            // 尝试解析为Long类型
            params.put("id", Long.parseLong(taskId));
        } catch (NumberFormatException e) {
            // 如果不是数字，可能是traceId，记录错误并返回提示
            logger.error("任务ID格式错误，应该是数字类型: {}", taskId);

            ClothingResponse errorResponse = new ClothingResponse();
            errorResponse.setCode(400);
            errorResponse.setMessage("任务ID格式错误，应该是数字类型的任务ID，而不是traceId");

            ClothingResponse.ClothingData data = new ClothingResponse.ClothingData();
            data.setTaskId(taskId);
            data.setStatus("error");
            data.setProgress(0);
            errorResponse.setData(data);

            return errorResponse;
        }

        apiRequest.setBizContent(JSON.toJSONString(params));
        
        try {
            CommonResponse response = getOpenClient().executeAppToken(apiRequest, appToken);

            if (response.isSuccess()) {
                String data = response.getData();
                logger.info("结果查询成功: {}", data);

                // 根据官方文档解析响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = JSON.parseObject(data, Map.class);

                ClothingResponse clothingResponse = new ClothingResponse();
                clothingResponse.setCode(200);
                clothingResponse.setMessage("success");

                ClothingResponse.ClothingData clothingData = new ClothingResponse.ClothingData();
                clothingData.setTaskId(taskId);

                if (responseData != null && responseData.get("data") != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> dataMap = (Map<String, Object>) responseData.get("data");

                    // 解析任务状态：1.排队中 2.生成中 3.成功 4.失败
                    Object statusObj = dataMap.get("status");
                    if (statusObj != null) {
                        int status = Integer.parseInt(statusObj.toString());
                        switch (status) {
                            case 1:
                                clothingData.setStatus("queuing");
                                clothingData.setProgress(10);
                                break;
                            case 2:
                                clothingData.setStatus("processing");
                                clothingData.setProgress(50);
                                break;
                            case 3:
                                clothingData.setStatus("completed");
                                clothingData.setProgress(100);

                                // 解析结果图片列表
                                Object resultListObj = dataMap.get("resultList");
                                if (resultListObj instanceof java.util.List) {
                                    @SuppressWarnings("unchecked")
                                    java.util.List<Map<String, Object>> resultList =
                                        (java.util.List<Map<String, Object>>) resultListObj;

                                    if (!resultList.isEmpty()) {
                                        // 收集所有成功的图片
                                        java.util.List<String> successfulImages = new java.util.ArrayList<>();
                                        for (Map<String, Object> result : resultList) {
                                            Object resultStatus = result.get("status");
                                            if (resultStatus != null && "1".equals(resultStatus.toString())) {
                                                Object url = result.get("url");
                                                if (url != null) {
                                                    successfulImages.add(url.toString());
                                                }
                                            }
                                        }

                                        // 设置多张图片列表
                                        if (!successfulImages.isEmpty()) {
                                            clothingData.setResultImages(successfulImages);
                                            logger.info("成功解析到 {} 张结果图片", successfulImages.size());
                                        }
                                    }
                                }
                                break;
                            case 4:
                                clothingData.setStatus("failed");
                                clothingData.setProgress(0);

                                // 设置错误信息
                                Object errorMsg = dataMap.get("errorMsg");
                                if (errorMsg != null) {
                                    clothingResponse.setMessage("任务失败: " + errorMsg.toString());
                                }
                                break;
                            default:
                                clothingData.setStatus("unknown");
                                clothingData.setProgress(0);
                        }
                    }
                }

                clothingResponse.setData(clothingData);
                return clothingResponse;
                
            } else {
                logger.error("结果查询失败: {}", response.getErrorMsg());
                
                // 如果查询接口不存在，返回一个默认响应
                if (response.getCode() == 40002 || response.getErrorMsg().contains("非法的参数")) {
                    logger.info("结果查询接口可能不存在，返回默认状态");
                    
                    ClothingResponse defaultResponse = new ClothingResponse();
                    defaultResponse.setCode(200);
                    defaultResponse.setMessage("任务已提交，请稍后查看结果");
                    
                    ClothingResponse.ClothingData data = new ClothingResponse.ClothingData();
                    data.setTaskId(taskId);
                    data.setStatus("submitted");
                    data.setProgress(0);
                    defaultResponse.setData(data);
                    
                    return defaultResponse;
                }
                
                ClothingResponse errorResponse = new ClothingResponse();
                errorResponse.setCode(response.getCode());
                errorResponse.setMessage(response.getErrorMsg());
                return errorResponse;
            }
            
        } catch (Exception e) {
            logger.error("结果查询异常", e);
            throw e;
        }
    }
}
