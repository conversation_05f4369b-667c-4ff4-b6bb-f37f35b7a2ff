package com.ziniao.service;

import com.ziniao.model.DataWriteBackRequest;
import com.ziniao.model.DataWriteBackResponse;
import com.ziniao.model.feishu.FeishuBitableUpdateRequest;
import com.ziniao.model.feishu.FeishuBitableUpdateResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据回写服务
 * 负责将AI处理结果或其他数据回写到飞书多维表格中
 */
@Service
public class DataWriteBackService {

    private static final Logger logger = LoggerFactory.getLogger(DataWriteBackService.class);

    @Autowired
    private FeishuBitableService bitableService;

    /**
     * 回写数据到多维表格
     *
     * @param request 回写请求参数
     * @return 回写结果
     * @throws Exception 回写失败时抛出异常
     */
    public DataWriteBackResponse writeBackData(DataWriteBackRequest request) throws Exception {
        logger.info("开始回写数据到多维表格: {}", request);

        try {
            // 验证请求参数
            validateRequest(request);

            // 构建飞书多维表格更新请求
            FeishuBitableUpdateRequest updateRequest = buildUpdateRequest(request);

            // 执行更新操作
            FeishuBitableUpdateResponse updateResponse = bitableService.updateRecord(updateRequest);

            if (updateResponse.isSuccess()) {
                // 构建成功响应
                DataWriteBackResponse.WriteBackData responseData = new DataWriteBackResponse.WriteBackData();
                responseData.setRecordId(request.getRecordId());
                responseData.setTaskId(request.getTaskId());
                responseData.setUpdatedFields(request.getFields());
                responseData.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                responseData.setWriteBackType(request.getWriteBackType());
                responseData.setRemark(request.getRemark());

                logger.info("数据回写成功: recordId={}, taskId={}, fields={}", 
                           request.getRecordId(), request.getTaskId(), request.getFields().keySet());

                return DataWriteBackResponse.success(responseData);
            } else {
                logger.error("数据回写失败: {}", updateResponse.getMsg());
                return DataWriteBackResponse.error(updateResponse.getCode(), 
                                                 "数据回写失败: " + updateResponse.getMsg());
            }

        } catch (Exception e) {
            logger.error("数据回写异常: recordId={}, taskId={}", 
                        request.getRecordId(), request.getTaskId(), e);
            throw e;
        }
    }

    /**
     * 根据任务ID回写AI处理结果
     *
     * @param taskId 任务ID
     * @param appToken 多维表格应用令牌
     * @param tableId 表格ID
     * @param recordId 记录ID
     * @param resultFields 结果字段数据
     * @return 回写结果
     * @throws Exception 回写失败时抛出异常
     */
    public DataWriteBackResponse writeBackAiResult(String taskId, String appToken, String tableId, 
                                                  String recordId, Map<String, Object> resultFields) throws Exception {
        logger.info("回写AI处理结果: taskId={}, recordId={}, fields={}", 
                   taskId, recordId, resultFields.keySet());

        // 构建回写请求
        DataWriteBackRequest request = new DataWriteBackRequest();
        request.setTaskId(taskId);
        request.setAppToken(appToken);
        request.setTableId(tableId);
        request.setRecordId(recordId);
        request.setFields(resultFields);
        request.setWriteBackType("ai_result");
        request.setRemark("AI处理结果自动回写");

        return writeBackData(request);
    }

    /**
     * 批量回写数据
     *
     * @param requests 批量回写请求列表
     * @return 批量回写结果
     */
    public Map<String, DataWriteBackResponse> batchWriteBack(java.util.List<DataWriteBackRequest> requests) {
        logger.info("开始批量回写数据，共 {} 条记录", requests.size());

        Map<String, DataWriteBackResponse> results = new HashMap<>();

        for (DataWriteBackRequest request : requests) {
            try {
                DataWriteBackResponse response = writeBackData(request);
                results.put(request.getRecordId(), response);
                
                if (response.isSuccess()) {
                    logger.debug("记录回写成功: recordId={}", request.getRecordId());
                } else {
                    logger.warn("记录回写失败: recordId={}, error={}", 
                               request.getRecordId(), response.getMessage());
                }
            } catch (Exception e) {
                logger.error("记录回写异常: recordId={}", request.getRecordId(), e);
                results.put(request.getRecordId(), 
                           DataWriteBackResponse.error(500, "回写异常: " + e.getMessage()));
            }
        }

        logger.info("批量回写完成，成功: {}, 失败: {}", 
                   results.values().stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum(),
                   results.values().stream().mapToInt(r -> r.isSuccess() ? 0 : 1).sum());

        return results;
    }

    /**
     * 构建飞书多维表格更新请求
     */
    private FeishuBitableUpdateRequest buildUpdateRequest(DataWriteBackRequest request) {
        FeishuBitableUpdateRequest updateRequest = new FeishuBitableUpdateRequest();
        updateRequest.setAppToken(request.getAppToken());
        updateRequest.setTableId(request.getTableId());
        updateRequest.setRecordId(request.getRecordId());
        updateRequest.setFields(request.getFields());
        return updateRequest;
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(DataWriteBackRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("回写请求不能为空");
        }
        if (request.getAppToken() == null || request.getAppToken().trim().isEmpty()) {
            throw new IllegalArgumentException("app_token不能为空");
        }
        if (request.getTableId() == null || request.getTableId().trim().isEmpty()) {
            throw new IllegalArgumentException("table_id不能为空");
        }
        if (request.getRecordId() == null || request.getRecordId().trim().isEmpty()) {
            throw new IllegalArgumentException("record_id不能为空");
        }
        if (request.getFields() == null || request.getFields().isEmpty()) {
            throw new IllegalArgumentException("回写字段不能为空");
        }
    }
}
