package com.ziniao.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ziniao.config.FeishuConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 飞书令牌服务
 * 负责获取和管理飞书应用访问令牌
 */
@Service
public class FeishuTokenService {

    private static final Logger logger = LoggerFactory.getLogger(FeishuTokenService.class);

    @Autowired
    private FeishuConfig feishuConfig;

    // 令牌缓存
    private final ConcurrentHashMap<String, TokenInfo> tokenCache = new ConcurrentHashMap<>();

    /**
     * 令牌信息内部类
     */
    private static class TokenInfo {
        private final String token;
        private final long expireTime;

        public TokenInfo(String token, long expireTime) {
            this.token = token;
            this.expireTime = expireTime;
        }

        public String getToken() {
            return token;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }

    /**
     * 获取应用访问令牌
     * 
     * @return 应用访问令牌
     * @throws Exception 获取令牌失败时抛出异常
     */
    public String getAppAccessToken() throws Exception {
        String cacheKey = "app_access_token";
        TokenInfo tokenInfo = tokenCache.get(cacheKey);

        // 检查缓存中的令牌是否有效
        if (tokenInfo != null && !tokenInfo.isExpired()) {
            logger.debug("使用缓存的应用访问令牌");
            return tokenInfo.getToken();
        }

        // 获取新的令牌
        logger.info("获取新的飞书应用访问令牌");
        String token = fetchAppAccessToken();

        // 缓存令牌
        long expireTime = System.currentTimeMillis() + (feishuConfig.getTokenCacheTime() * 1000L);
        tokenCache.put(cacheKey, new TokenInfo(token, expireTime));

        return token;
    }

    /**
     * 强制刷新应用访问令牌
     * 
     * @return 新的应用访问令牌
     * @throws Exception 获取令牌失败时抛出异常
     */
    public String forceRefreshAppAccessToken() throws Exception {
        logger.info("强制刷新飞书应用访问令牌");
        
        // 清除缓存
        tokenCache.remove("app_access_token");
        
        // 获取新令牌
        return getAppAccessToken();
    }

    /**
     * 从飞书API获取应用访问令牌
     * 
     * @return 应用访问令牌
     * @throws Exception 获取令牌失败时抛出异常
     */
    private String fetchAppAccessToken() throws Exception {
        String url = feishuConfig.getBaseUrl() + "/open-apis/auth/v3/app_access_token/internal";
        
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("app_id", feishuConfig.getAppId());
        requestBody.put("app_secret", feishuConfig.getAppSecret());

        logger.info("请求飞书应用访问令牌，URL: {}", url);
        logger.debug("请求参数: app_id={}", feishuConfig.getAppId());

        HttpURLConnection connection = null;
        try {
            // 创建连接
            URL urlObj = new URL(url);
            connection = (HttpURLConnection) urlObj.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setConnectTimeout(feishuConfig.getConnectTimeout());
            connection.setReadTimeout(feishuConfig.getReadTimeout());
            connection.setDoOutput(true);

            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.toJSONString().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            StringBuilder response = new StringBuilder();
            
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ? 
                    connection.getInputStream() : connection.getErrorStream(), 
                    StandardCharsets.UTF_8))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }

            logger.info("飞书API响应状态码: {}", responseCode);
            logger.debug("飞书API响应内容: {}", response.toString());

            if (responseCode != 200) {
                throw new Exception("获取飞书应用访问令牌失败，状态码: " + responseCode + ", 响应: " + response.toString());
            }

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response.toString());
            int code = responseJson.getIntValue("code");
            
            if (code != 0) {
                String msg = responseJson.getString("msg");
                throw new Exception("获取飞书应用访问令牌失败，错误码: " + code + ", 错误信息: " + msg);
            }

            String appAccessToken = responseJson.getString("app_access_token");
            if (appAccessToken == null || appAccessToken.trim().isEmpty()) {
                throw new Exception("获取飞书应用访问令牌失败，响应中未包含有效的令牌");
            }

            logger.info("成功获取飞书应用访问令牌");
            return appAccessToken;

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 清除所有缓存的令牌
     */
    public void clearTokenCache() {
        logger.info("清除飞书令牌缓存");
        tokenCache.clear();
    }

    /**
     * 检查令牌是否有效
     * 
     * @param token 要检查的令牌
     * @return 令牌是否有效
     */
    public boolean isTokenValid(String token) {
        return token != null && !token.trim().isEmpty();
    }
}
