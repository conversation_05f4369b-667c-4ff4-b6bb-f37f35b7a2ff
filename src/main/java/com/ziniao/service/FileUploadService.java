package com.ziniao.service;

import com.ziniao.model.FileUploadResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传服务类
 */
@Service
public class FileUploadService {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadService.class);

    @Value("${file.upload.path:uploads/}")
    private String uploadPath;

    @Value("${file.upload.max-size:209715200}")
    private long maxFileSize;

    @Value("${file.upload.allowed-types:jpg,jpeg,png,gif,bmp,webp,pdf,doc,docx,txt,zip,rar,mp4,avi,mov,mp3,wav}")
    private String allowedTypes;

    @Value("${file.upload.url-prefix:/uploads/}")
    private String urlPrefix;

    @Value("${file.upload.server-base-url:http://localhost:8080}")
    private String serverBaseUrl;

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${image.proxy.enabled:true}")
    private boolean imageProxyEnabled;

    @Autowired(required = false)
    private ImageProxyService imageProxyService;

    /**
     * 上传文件
     *
     * @param file    上传的文件
     * @param request HTTP请求对象，用于获取服务器信息
     * @return 上传结果
     */
    public FileUploadResponse uploadFile(MultipartFile file, HttpServletRequest request) {
        try {
            logger.info("开始处理文件上传: {}", file.getOriginalFilename());

            // 1. 验证文件
            FileUploadResponse validationResult = validateFile(file);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 2. 生成文件名和路径
            String originalFileName = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFileName);
            String newFileName = generateFileName(originalFileName, fileExtension);
            
            // 3. 创建按日期分组的目录结构
            String dateFolder = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String relativePath = urlPrefix + dateFolder + "/" + newFileName;
            String fullPath = uploadPath + dateFolder + "/" + newFileName;

            // 4. 确保目录存在
            Path targetPath = Paths.get(fullPath);
            Files.createDirectories(targetPath.getParent());

            // 5. 保存文件
            file.transferTo(targetPath.toFile());
            logger.info("文件保存成功: {}", fullPath);

            // 6. 生成完整的访问URL
            String fileUrl = buildFileUrl(request, relativePath);

            // 7. 创建文件信息
            FileUploadResponse.FileInfo fileInfo = new FileUploadResponse.FileInfo(
                    originalFileName,
                    newFileName,
                    file.getSize(),
                    file.getContentType(),
                    fileUrl,
                    relativePath,
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
            );

            logger.info("文件上传成功: {}", fileInfo);
            return FileUploadResponse.success(fileInfo);

        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return FileUploadResponse.error("文件保存失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("文件上传异常", e);
            return FileUploadResponse.error("文件上传异常: " + e.getMessage());
        }
    }

    /**
     * 验证文件
     */
    private FileUploadResponse validateFile(MultipartFile file) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            return FileUploadResponse.error(400, "文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            return FileUploadResponse.error(400, 
                    String.format("文件大小超过限制，最大允许 %d MB", maxFileSize / 1024 / 1024));
        }

        // 检查文件类型
        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null || originalFileName.trim().isEmpty()) {
            return FileUploadResponse.error(400, "文件名不能为空");
        }

        String fileExtension = getFileExtension(originalFileName).toLowerCase();
        List<String> allowedTypeList = Arrays.asList(allowedTypes.toLowerCase().split(","));
        
        if (!allowedTypeList.contains(fileExtension)) {
            return FileUploadResponse.error(400, 
                    String.format("不支持的文件类型: %s，支持的类型: %s", fileExtension, allowedTypes));
        }

        return FileUploadResponse.success(null);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 生成新的文件名
     */
    private String generateFileName(String originalFileName, String extension) {
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return timestamp + "_" + uuid + "." + extension;
    }

    /**
     * 构建文件访问URL
     */
    private String buildFileUrl(HttpServletRequest request, String relativePath) {
        // 如果启用了图片代理且是图片文件，使用代理URL
        if (imageProxyEnabled && imageProxyService != null && isImageFile(relativePath)) {
            logger.debug("使用图片代理URL: {}", relativePath);
            String proxyUrl = imageProxyService.generateProxyUrl(relativePath, request);
            if (proxyUrl != null) {
                return proxyUrl;
            }
            logger.warn("生成代理URL失败，回退到直接URL: {}", relativePath);
        }

        // 使用传统的直接URL方式
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int port = request.getServerPort();
        String contextPath = request.getContextPath();

        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);

        // 只有在非标准端口时才添加端口号
        if ((!"http".equals(scheme) || port != 80) && (!"https".equals(scheme) || port != 443)) {
            url.append(":").append(port);
        }

        url.append(contextPath);

        // 确保relativePath以/开头
        if (!relativePath.startsWith("/")) {
            url.append("/");
        }
        url.append(relativePath);

        return url.toString();
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }

        String extension = getFileExtension(filePath).toLowerCase();
        return Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp", "svg").contains(extension);
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String relativePath) {
        try {
            if (relativePath == null || relativePath.trim().isEmpty()) {
                return false;
            }

            // 移除URL前缀，获取实际的相对路径
            String actualPath = relativePath;
            if (actualPath.startsWith(urlPrefix)) {
                actualPath = actualPath.substring(urlPrefix.length());
            }

            String fullPath = uploadPath + actualPath;
            Path filePath = Paths.get(fullPath);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                logger.info("文件删除成功: {}", fullPath);
                return true;
            } else {
                logger.warn("要删除的文件不存在: {}", fullPath);
                return false;
            }
        } catch (IOException e) {
            logger.error("文件删除失败: " + relativePath, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String relativePath) {
        try {
            if (relativePath == null || relativePath.trim().isEmpty()) {
                return false;
            }

            String actualPath = relativePath;
            if (actualPath.startsWith(urlPrefix)) {
                actualPath = actualPath.substring(urlPrefix.length());
            }

            String fullPath = uploadPath + actualPath;
            return Files.exists(Paths.get(fullPath));
        } catch (Exception e) {
            logger.error("检查文件存在性失败: " + relativePath, e);
            return false;
        }
    }

    /**
     * 获取文件信息
     */
    public File getFile(String relativePath) {
        try {
            if (relativePath == null || relativePath.trim().isEmpty()) {
                return null;
            }

            String actualPath = relativePath;
            if (actualPath.startsWith(urlPrefix)) {
                actualPath = actualPath.substring(urlPrefix.length());
            }

            String fullPath = uploadPath + actualPath;
            File file = new File(fullPath);

            return file.exists() ? file : null;
        } catch (Exception e) {
            logger.error("获取文件失败: " + relativePath, e);
            return null;
        }
    }

    /**
     * 从URL下载图片并保存到服务器
     *
     * @param imageUrl 图片URL
     * @param request  HTTP请求对象，用于获取服务器信息
     * @return 上传结果
     */
    public FileUploadResponse downloadImageFromUrl(String imageUrl, HttpServletRequest request) {
        try {
            logger.info("开始从URL下载图片: {}", imageUrl);

            // 1. 验证URL
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                return FileUploadResponse.error(400, "图片URL不能为空");
            }

            // 2. 创建URL连接
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            // 3. 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                return FileUploadResponse.error(400, "无法访问图片URL，响应状态: " + responseCode);
            }

            // 4. 获取内容类型和文件大小
            String contentType = connection.getContentType();
            long contentLength = connection.getContentLengthLong();

            logger.info("图片信息 - Content-Type: {}, Content-Length: {}", contentType, contentLength);

            // 5. 验证内容类型
            if (contentType == null || !contentType.startsWith("image/")) {
                return FileUploadResponse.error(400, "URL指向的不是图片文件，Content-Type: " + contentType);
            }

            // 6. 检查文件大小
            if (contentLength > maxFileSize) {
                return FileUploadResponse.error(400,
                        String.format("图片文件过大，最大允许 %d MB", maxFileSize / 1024 / 1024));
            }

            // 7. 从URL中提取文件扩展名，如果没有则根据Content-Type推断
            String fileExtension = extractExtensionFromUrl(imageUrl, contentType);

            // 8. 验证文件类型
            List<String> allowedTypeList = Arrays.asList(allowedTypes.toLowerCase().split(","));
            if (!allowedTypeList.contains(fileExtension.toLowerCase())) {
                return FileUploadResponse.error(400,
                        String.format("不支持的图片类型: %s，支持的类型: %s", fileExtension, allowedTypes));
            }

            // 9. 生成文件名和路径
            String originalFileName = extractFileNameFromUrl(imageUrl);
            String newFileName = generateFileName(originalFileName, fileExtension);

            // 10. 创建按日期分组的目录结构
            String dateFolder = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String relativePath = urlPrefix + dateFolder + "/" + newFileName;
            String fullPath = uploadPath + dateFolder + "/" + newFileName;

            // 11. 确保目录存在
            Path targetPath = Paths.get(fullPath);
            Files.createDirectories(targetPath.getParent());

            // 12. 下载并保存文件
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(targetPath.toFile())) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytesRead = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }

                logger.info("图片下载完成，总大小: {} bytes", totalBytesRead);
            }

            // 13. 生成完整的访问URL
            String fileUrl = buildFileUrl(request, relativePath);

            // 14. 创建文件信息
            FileUploadResponse.FileInfo fileInfo = new FileUploadResponse.FileInfo(
                    originalFileName,
                    newFileName,
                    Files.size(targetPath),
                    contentType,
                    fileUrl,
                    relativePath,
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
            );

            logger.info("图片从URL保存成功: {}", fileInfo);
            return FileUploadResponse.success(fileInfo);

        } catch (IOException e) {
            logger.error("从URL下载图片失败: " + imageUrl, e);
            return FileUploadResponse.error("图片下载失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("从URL下载图片异常: " + imageUrl, e);
            return FileUploadResponse.error("图片下载异常: " + e.getMessage());
        }
    }

    /**
     * 从URL中提取文件扩展名
     */
    private String extractExtensionFromUrl(String imageUrl, String contentType) {
        // 首先尝试从URL中提取扩展名
        String extension = "";
        if (imageUrl.contains(".")) {
            String urlPath = imageUrl.split("\\?")[0]; // 移除查询参数
            int lastDotIndex = urlPath.lastIndexOf(".");
            if (lastDotIndex != -1 && lastDotIndex < urlPath.length() - 1) {
                extension = urlPath.substring(lastDotIndex + 1).toLowerCase();
            }
        }

        // 如果从URL中无法提取扩展名，则根据Content-Type推断
        if (extension.isEmpty() && contentType != null) {
            switch (contentType.toLowerCase()) {
                case "image/jpeg":
                    extension = "jpg";
                    break;
                case "image/png":
                    extension = "png";
                    break;
                case "image/gif":
                    extension = "gif";
                    break;
                case "image/webp":
                    extension = "webp";
                    break;
                case "image/bmp":
                    extension = "bmp";
                    break;
                default:
                    extension = "jpg"; // 默认为jpg
                    break;
            }
        }

        return extension.isEmpty() ? "jpg" : extension;
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String imageUrl) {
        try {
            String urlPath = imageUrl.split("\\?")[0]; // 移除查询参数
            int lastSlashIndex = urlPath.lastIndexOf("/");
            if (lastSlashIndex != -1 && lastSlashIndex < urlPath.length() - 1) {
                return urlPath.substring(lastSlashIndex + 1);
            }
        } catch (Exception e) {
            logger.warn("无法从URL提取文件名: " + imageUrl, e);
        }
        return "downloaded_image";
    }

    /**
     * 直接从URL下载图片并返回本地访问URL
     *
     * @param imageUrl 图片URL
     * @param originalName 原始文件名（可选）
     * @return 本地访问URL，失败时返回null
     */
    public String downloadImageFromUrlDirect(String imageUrl, String originalName) {
        try {
            logger.info("直接下载图片: {} (原始名称: {})", imageUrl, originalName);

            // 1. 验证URL格式
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                logger.error("图片URL为空");
                return null;
            }

            if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://")) {
                logger.error("无效的图片URL格式: {}", imageUrl);
                return null;
            }

            // 2. 创建HTTP连接
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; ZiniaoBot/1.0)");

            // 3. 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                logger.error("HTTP请求失败，状态码: {}", responseCode);
                return null;
            }

            // 4. 获取内容类型和文件大小
            String contentType = connection.getContentType();
            long contentLength = connection.getContentLengthLong();

            logger.info("图片信息 - Content-Type: {}, Content-Length: {}", contentType, contentLength);

            // 5. 验证内容类型
            if (contentType == null || !contentType.startsWith("image/")) {
                logger.error("URL指向的不是图片文件，Content-Type: {}", contentType);
                return null;
            }

            // 6. 检查文件大小
            if (contentLength > maxFileSize) {
                logger.error("图片文件过大，大小: {} bytes, 最大允许: {} bytes", contentLength, maxFileSize);
                return null;
            }

            // 7. 从URL中提取文件扩展名，如果没有则根据Content-Type推断
            String fileExtension = extractExtensionFromUrl(imageUrl, contentType);

            // 8. 生成文件名和路径
            String finalOriginalName = (originalName != null && !originalName.trim().isEmpty()) ?
                    originalName : extractFileNameFromUrl(imageUrl);
            String newFileName = generateFileName(finalOriginalName, fileExtension);

            // 9. 创建按日期分组的目录结构
            String dateFolder = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String relativePath = urlPrefix + dateFolder + "/" + newFileName;
            String fullPath = uploadPath + dateFolder + "/" + newFileName;

            // 10. 确保目录存在
            Path targetPath = Paths.get(fullPath);
            Files.createDirectories(targetPath.getParent());

            // 11. 下载并保存文件
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(targetPath.toFile())) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytesRead = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }

                logger.info("图片下载完成，总大小: {} bytes", totalBytesRead);
            }

            // 12. 生成完整的访问URL（使用配置的服务器基础URL）
            String fileUrl = serverBaseUrl + relativePath;

            logger.info("图片直接下载成功: {} -> {}", imageUrl, fileUrl);
            return fileUrl;

        } catch (Exception e) {
            logger.error("直接下载图片失败: {}", imageUrl, e);
            return null;
        }
    }
}
