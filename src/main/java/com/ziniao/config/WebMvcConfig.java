package com.ziniao.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * Web MVC配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfig.class);

    @Value("${file.upload.path:uploads/}")
    private String uploadPath;

    @Value("${file.upload.url-prefix:/uploads/}")
    private String urlPrefix;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Swagger UI资源映射
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

        // Knife4j资源映射
        registry.addResourceHandler("doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        // 文件上传静态资源映射
        // 将 /uploads/** 请求映射到实际的文件存储目录
        String resourceLocation = "file:" + new File(uploadPath).getAbsolutePath() + "/";

        logger.info("=== 静态资源映射配置 ===");
        logger.info("上传路径配置: {}", uploadPath);
        logger.info("URL前缀配置: {}", urlPrefix);
        logger.info("资源位置: {}", resourceLocation);
        logger.info("映射模式: {}", urlPrefix + "**");

        registry.addResourceHandler(urlPrefix + "**")
                .addResourceLocations(resourceLocation)
                .setCachePeriod(3600); // 设置缓存时间为1小时

        logger.info("静态资源映射配置完成");
    }
}
