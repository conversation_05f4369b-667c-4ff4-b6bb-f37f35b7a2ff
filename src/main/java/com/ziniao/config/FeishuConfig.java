package com.ziniao.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 飞书API配置类
 */
@Configuration
@ConfigurationProperties(prefix = "feishu.api")
public class FeishuConfig {

    /**
     * 飞书API基础URL
     */
    private String baseUrl = "https://open.feishu.cn";

    /**
     * 飞书应用ID
     */
    private String appId = "cli_a8fe3e73bd78d00d";

    /**
     * 飞书应用密钥
     */
    private String appSecret = "whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU";

    /**
     * 请求超时时间（毫秒）
     */
    private int timeout = 30000;

    /**
     * 令牌缓存时间（秒）
     * 飞书应用访问令牌有效期为2小时，这里设置为7000秒（约1小时55分钟）留有余量
     */
    private int tokenCacheTime = 7000;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public int getTokenCacheTime() {
        return tokenCacheTime;
    }

    public void setTokenCacheTime(int tokenCacheTime) {
        this.tokenCacheTime = tokenCacheTime;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
