package com.ziniao.demo;

import com.ziniao.model.ClothingRequest;
import com.ziniao.model.ClothingResponse;
import com.ziniao.service.ClothingService;
import com.ziniao.service.TokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * AI穿衣Demo主类
 */
@Component
public class ClothingDemo implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ClothingDemo.class);

    @Autowired
    private ClothingService clothingService;

    @Autowired
    private TokenService tokenService;

    @Override
    public void run(String... args) throws Exception {
        logger.info("=== 紫鸟AI穿衣Demo应用已启动 ===");
        logger.info("应用地址: http://localhost:8080");
        logger.info("API接口:");
        logger.info("  令牌服务: http://localhost:8080/api/token/health");
        logger.info("  穿衣服务: http://localhost:8080/api/clothing/health");
        logger.info("  获取令牌: curl -X GET http://localhost:8080/api/token/app");
        logger.info("=== Demo启动完成，可以通过API接口测试功能 ===");

        // 注释掉自动执行的示例，避免启动时的依赖问题
        // 用户可以通过API接口手动测试
        /*
        try {
            // 示例1：令牌获取
            tokenExample();

            // 示例2：基本穿衣功能
            // basicClothingExample();

            // 示例3：异步任务处理
            // asyncTaskExample();

        } catch (Exception e) {
            logger.error("Demo执行失败", e);
        }
        */
    }
    
    /**
     * 令牌获取示例
     */
    public void tokenExample() throws Exception {
        logger.info("=== 令牌获取示例 ===");

        try {
            String token = tokenService.getAppToken();
            logger.info("成功获取应用令牌: {}", token);

            boolean isValid = tokenService.validateToken(token);
            logger.info("令牌验证结果: {}", isValid ? "有效" : "无效");

        } catch (Exception e) {
            logger.error("令牌获取失败", e);
            throw e;
        }
    }

    /**
     * 基本穿衣功能示例
     */
    public void basicClothingExample() throws Exception {
        logger.info("=== 基本穿衣功能示例 ===");

        // 创建请求参数
        ClothingRequest request = new ClothingRequest();
        request.setPersonImage("https://example.com/person.jpg"); // 人物图片URL或base64
        request.setClothingImage("https://example.com/clothing.jpg"); // 服装图片URL或base64
        request.setClothingType("上衣"); // 服装类型：上衣、下装等
        request.setStyle("casual"); // 风格参数
        request.setQuality("high"); // 质量参数

        // 调用API
        ClothingResponse response = clothingService.processClothing(request);

        // 处理结果
        if (response.isSuccess()) {
            logger.info("穿衣处理成功！");
            if (response.getData() != null) {
                logger.info("结果图片: {}", response.getData().getResultImage());
                logger.info("任务ID: {}", response.getData().getTaskId());
                logger.info("状态: {}", response.getData().getStatus());
            }
        } else {
            logger.error("穿衣处理失败: {}", response.getMessage());
        }
    }
    
    /**
     * 异步任务处理示例
     */
    public void asyncTaskExample() throws Exception {
        logger.info("=== 异步任务处理示例 ===");

        // 创建请求参数
        ClothingRequest request = new ClothingRequest();
        request.setPersonImage("https://example.com/person2.jpg");
        request.setClothingImage("https://example.com/dress.jpg");
        request.setClothingType("连衣裙");

        // 提交任务
        ClothingResponse initialResponse = clothingService.processClothing(request);

        if (initialResponse.isSuccess() && initialResponse.getData() != null) {
            String taskId = initialResponse.getData().getTaskId();

            if (taskId != null && !taskId.isEmpty()) {
                logger.info("任务已提交，任务ID: {}", taskId);

                // 等待任务完成
                ClothingResponse finalResponse = clothingService.waitForTaskCompletion(
                    taskId,
                    300000, // 最大等待5分钟
                    5000    // 每5秒轮询一次
                );

                if (finalResponse.isSuccess() && finalResponse.getData() != null) {
                    logger.info("任务完成！结果图片: {}", finalResponse.getData().getResultImage());
                } else {
                    logger.error("任务失败: {}", finalResponse.getMessage());
                }
            }
        }
    }
    
    /**
     * 批量处理示例
     */
    public void batchProcessingExample() throws Exception {
        logger.info("=== 批量处理示例 ===");

        // 模拟多个穿衣请求
        String[] personImages = {
            "https://example.com/person1.jpg",
            "https://example.com/person2.jpg",
            "https://example.com/person3.jpg"
        };

        String[] clothingImages = {
            "https://example.com/shirt1.jpg",
            "https://example.com/shirt2.jpg",
            "https://example.com/shirt3.jpg"
        };

        for (int i = 0; i < personImages.length; i++) {
            try {
                ClothingRequest request = new ClothingRequest();
                request.setPersonImage(personImages[i]);
                request.setClothingImage(clothingImages[i]);
                request.setClothingType("上衣");

                ClothingResponse response = clothingService.processClothing(request);

                logger.info("批次 {} 处理结果: {}", i + 1, response.isSuccess() ? "成功" : "失败");

                // 避免请求过于频繁
                Thread.sleep(1000);

            } catch (Exception e) {
                logger.error("批次 {} 处理失败", i + 1, e);
            }
        }
    }
}
