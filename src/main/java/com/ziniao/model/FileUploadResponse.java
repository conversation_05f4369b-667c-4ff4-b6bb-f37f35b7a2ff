package com.ziniao.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 文件上传响应模型
 */
@ApiModel(description = "文件上传响应")
public class FileUploadResponse {

    @ApiModelProperty(value = "响应状态码", example = "200")
    private int code;

    @ApiModelProperty(value = "响应消息", example = "上传成功")
    private String message;

    @ApiModelProperty(value = "是否成功", example = "true")
    private boolean success;

    @ApiModelProperty(value = "文件信息")
    private FileInfo data;

    /**
     * 文件信息内部类
     */
    @ApiModel(description = "文件信息")
    public static class FileInfo {
        
        @ApiModelProperty(value = "原始文件名", example = "example.jpg")
        private String originalFileName;

        @ApiModelProperty(value = "存储文件名", example = "20250720_123456_abc123.jpg")
        private String fileName;

        @ApiModelProperty(value = "文件大小（字节）", example = "1024000")
        private long fileSize;

        @ApiModelProperty(value = "文件类型", example = "image/jpeg")
        private String contentType;

        @ApiModelProperty(value = "文件完整访问URL", example = "http://localhost:8080/uploads/2025/07/20/20250720_123456_abc123.jpg")
        private String fileUrl;

        @ApiModelProperty(value = "相对路径", example = "/uploads/2025/07/20/20250720_123456_abc123.jpg")
        private String relativePath;

        @ApiModelProperty(value = "上传时间", example = "2025-07-20 12:34:56")
        private String uploadTime;

        // 构造函数
        public FileInfo() {}

        public FileInfo(String originalFileName, String fileName, long fileSize, 
                       String contentType, String fileUrl, String relativePath, String uploadTime) {
            this.originalFileName = originalFileName;
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.contentType = contentType;
            this.fileUrl = fileUrl;
            this.relativePath = relativePath;
            this.uploadTime = uploadTime;
        }

        // Getters and Setters
        public String getOriginalFileName() {
            return originalFileName;
        }

        public void setOriginalFileName(String originalFileName) {
            this.originalFileName = originalFileName;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public long getFileSize() {
            return fileSize;
        }

        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }

        public String getContentType() {
            return contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getRelativePath() {
            return relativePath;
        }

        public void setRelativePath(String relativePath) {
            this.relativePath = relativePath;
        }

        public String getUploadTime() {
            return uploadTime;
        }

        public void setUploadTime(String uploadTime) {
            this.uploadTime = uploadTime;
        }

        @Override
        public String toString() {
            return "FileInfo{" +
                    "originalFileName='" + originalFileName + '\'' +
                    ", fileName='" + fileName + '\'' +
                    ", fileSize=" + fileSize +
                    ", contentType='" + contentType + '\'' +
                    ", fileUrl='" + fileUrl + '\'' +
                    ", relativePath='" + relativePath + '\'' +
                    ", uploadTime='" + uploadTime + '\'' +
                    '}';
        }
    }

    // 构造函数
    public FileUploadResponse() {}

    public FileUploadResponse(int code, String message, boolean success) {
        this.code = code;
        this.message = message;
        this.success = success;
    }

    public FileUploadResponse(int code, String message, boolean success, FileInfo data) {
        this.code = code;
        this.message = message;
        this.success = success;
        this.data = data;
    }

    // 静态工厂方法
    public static FileUploadResponse success(FileInfo fileInfo) {
        return new FileUploadResponse(200, "上传成功", true, fileInfo);
    }

    public static FileUploadResponse error(String message) {
        return new FileUploadResponse(500, message, false);
    }

    public static FileUploadResponse error(int code, String message) {
        return new FileUploadResponse(code, message, false);
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public FileInfo getData() {
        return data;
    }

    public void setData(FileInfo data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "FileUploadResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", success=" + success +
                ", data=" + data +
                '}';
    }
}
