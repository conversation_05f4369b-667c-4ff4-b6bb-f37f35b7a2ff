package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * AI穿衣响应结果
 */
@ApiModel(description = "AI穿衣响应结果")
public class ClothingResponse {

    @JSONField(name = "code")
    @ApiModelProperty(value = "响应状态码", example = "200")
    private int code;

    @JSONField(name = "message")
    @ApiModelProperty(value = "响应消息", example = "success")
    private String message;

    @JSONField(name = "data")
    @ApiModelProperty(value = "响应数据")
    private ClothingData data;

    @JSONField(name = "request_id")
    @ApiModelProperty(value = "请求ID")
    private String requestId;
    
    public ClothingResponse() {
    }
    
    // Getters and Setters
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public ClothingData getData() {
        return data;
    }
    
    public void setData(ClothingData data) {
        this.data = data;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public boolean isSuccess() {
        return code == 200;
    }
    
    @Override
    public String toString() {
        return "ClothingResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", requestId='" + requestId + '\'' +
                '}';
    }
    
    /**
     * 响应数据内部类
     */
    @ApiModel(description = "穿衣响应数据")
    public static class ClothingData {

        @JSONField(name = "result_image")
        @ApiModelProperty(value = "结果图片URL（兼容单张图片）", example = "https://example.com/result.jpg")
        private String resultImage;

        @JSONField(name = "result_images")
        @ApiModelProperty(value = "结果图片URL列表（支持多张图片）")
        private java.util.List<String> resultImages;

        @JSONField(name = "task_id")
        @ApiModelProperty(value = "任务ID", example = "task_123456")
        private String taskId;

        @JSONField(name = "status")
        @ApiModelProperty(value = "任务状态", example = "completed",
                         allowableValues = "submitted,queuing,processing,completed,failed")
        private String status;

        @JSONField(name = "progress")
        @ApiModelProperty(value = "处理进度", example = "100")
        private int progress;
        
        public ClothingData() {
        }
        
        // Getters and Setters
        public String getResultImage() {
            return resultImage;
        }

        public void setResultImage(String resultImage) {
            this.resultImage = resultImage;
        }

        public java.util.List<String> getResultImages() {
            return resultImages;
        }

        public void setResultImages(java.util.List<String> resultImages) {
            this.resultImages = resultImages;
            // 为了向后兼容，如果有多张图片，将第一张设置为单张图片字段
            if (resultImages != null && !resultImages.isEmpty()) {
                this.resultImage = resultImages.get(0);
            }
        }
        
        public String getTaskId() {
            return taskId;
        }
        
        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
        
        public int getProgress() {
            return progress;
        }
        
        public void setProgress(int progress) {
            this.progress = progress;
        }
        
        @Override
        public String toString() {
            return "ClothingData{" +
                    "resultImage='" + resultImage + '\'' +
                    ", resultImages=" + resultImages +
                    ", taskId='" + taskId + '\'' +
                    ", status='" + status + '\'' +
                    ", progress=" + progress +
                    '}';
        }
    }
}
