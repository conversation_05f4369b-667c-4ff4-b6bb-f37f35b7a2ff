package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 飞书多维表格图片下载响应数据
 */
@ApiModel(description = "飞书多维表格图片下载响应数据")
public class FeishuImageDownloadResponse {

    @ApiModelProperty(value = "响应状态码")
    private int code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "是否成功")
    private boolean success;

    @ApiModelProperty(value = "响应数据")
    private Data data;

    /**
     * 响应数据
     */
    @ApiModel(description = "响应数据")
    public static class Data {
        @ApiModelProperty(value = "处理的记录总数")
        private int totalRecords;

        @ApiModelProperty(value = "找到的图片总数")
        private int totalImages;

        @ApiModelProperty(value = "成功下载的图片数")
        private int successfulDownloads;

        @ApiModelProperty(value = "下载失败的图片数")
        private int failedDownloads;

        @ApiModelProperty(value = "处理的记录列表")
        private List<RecordImageInfo> records;

        @ApiModelProperty(value = "是否还有更多记录")
        private boolean hasMore;

        @ApiModelProperty(value = "下一页的分页标记")
        private String nextPageToken;

        @ApiModelProperty(value = "处理耗时（毫秒）")
        private long processingTime;

        // Getters and Setters
        public int getTotalRecords() {
            return totalRecords;
        }

        public void setTotalRecords(int totalRecords) {
            this.totalRecords = totalRecords;
        }

        public int getTotalImages() {
            return totalImages;
        }

        public void setTotalImages(int totalImages) {
            this.totalImages = totalImages;
        }

        public int getSuccessfulDownloads() {
            return successfulDownloads;
        }

        public void setSuccessfulDownloads(int successfulDownloads) {
            this.successfulDownloads = successfulDownloads;
        }

        public int getFailedDownloads() {
            return failedDownloads;
        }

        public void setFailedDownloads(int failedDownloads) {
            this.failedDownloads = failedDownloads;
        }

        public List<RecordImageInfo> getRecords() {
            return records;
        }

        public void setRecords(List<RecordImageInfo> records) {
            this.records = records;
        }

        public boolean isHasMore() {
            return hasMore;
        }

        public void setHasMore(boolean hasMore) {
            this.hasMore = hasMore;
        }

        public String getNextPageToken() {
            return nextPageToken;
        }

        public void setNextPageToken(String nextPageToken) {
            this.nextPageToken = nextPageToken;
        }

        public long getProcessingTime() {
            return processingTime;
        }

        public void setProcessingTime(long processingTime) {
            this.processingTime = processingTime;
        }
    }

    /**
     * 记录图片信息
     */
    @ApiModel(description = "记录图片信息")
    public static class RecordImageInfo {
        @ApiModelProperty(value = "记录ID")
        private String recordId;

        @ApiModelProperty(value = "记录中的图片字段信息")
        private Map<String, List<ImageInfo>> imageFields;

        @ApiModelProperty(value = "该记录的图片总数")
        private int imageCount;

        @ApiModelProperty(value = "该记录成功下载的图片数")
        private int successCount;

        @ApiModelProperty(value = "该记录下载失败的图片数")
        private int failureCount;

        // Getters and Setters
        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public Map<String, List<ImageInfo>> getImageFields() {
            return imageFields;
        }

        public void setImageFields(Map<String, List<ImageInfo>> imageFields) {
            this.imageFields = imageFields;
        }

        public int getImageCount() {
            return imageCount;
        }

        public void setImageCount(int imageCount) {
            this.imageCount = imageCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(int failureCount) {
            this.failureCount = failureCount;
        }
    }

    /**
     * 图片信息
     */
    @ApiModel(description = "图片信息")
    public static class ImageInfo {
        @ApiModelProperty(value = "原始文件token")
        private String fileToken;

        @ApiModelProperty(value = "原始文件名")
        private String originalName;

        @ApiModelProperty(value = "文件类型")
        private String fileType;

        @ApiModelProperty(value = "文件大小")
        private long fileSize;

        @ApiModelProperty(value = "飞书原始URL")
        private String originalUrl;

        @ApiModelProperty(value = "飞书临时下载URL")
        private String tmpDownloadUrl;

        @ApiModelProperty(value = "本地文件路径")
        private String localFilePath;

        @ApiModelProperty(value = "本地访问URL")
        private String localAccessUrl;

        @ApiModelProperty(value = "下载状态")
        private String downloadStatus;

        @ApiModelProperty(value = "下载错误信息")
        private String errorMessage;

        @ApiModelProperty(value = "下载时间")
        private String downloadTime;

        // Getters and Setters
        public String getFileToken() {
            return fileToken;
        }

        public void setFileToken(String fileToken) {
            this.fileToken = fileToken;
        }

        public String getOriginalName() {
            return originalName;
        }

        public void setOriginalName(String originalName) {
            this.originalName = originalName;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public long getFileSize() {
            return fileSize;
        }

        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }

        public String getOriginalUrl() {
            return originalUrl;
        }

        public void setOriginalUrl(String originalUrl) {
            this.originalUrl = originalUrl;
        }

        public String getTmpDownloadUrl() {
            return tmpDownloadUrl;
        }

        public void setTmpDownloadUrl(String tmpDownloadUrl) {
            this.tmpDownloadUrl = tmpDownloadUrl;
        }

        public String getLocalFilePath() {
            return localFilePath;
        }

        public void setLocalFilePath(String localFilePath) {
            this.localFilePath = localFilePath;
        }

        public String getLocalAccessUrl() {
            return localAccessUrl;
        }

        public void setLocalAccessUrl(String localAccessUrl) {
            this.localAccessUrl = localAccessUrl;
        }

        public String getDownloadStatus() {
            return downloadStatus;
        }

        public void setDownloadStatus(String downloadStatus) {
            this.downloadStatus = downloadStatus;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getDownloadTime() {
            return downloadTime;
        }

        public void setDownloadTime(String downloadTime) {
            this.downloadTime = downloadTime;
        }
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    /**
     * 创建成功响应
     */
    public static FeishuImageDownloadResponse success(Data data) {
        FeishuImageDownloadResponse response = new FeishuImageDownloadResponse();
        response.setCode(200);
        response.setMessage("处理成功");
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    /**
     * 创建错误响应
     */
    public static FeishuImageDownloadResponse error(int code, String message) {
        FeishuImageDownloadResponse response = new FeishuImageDownloadResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setSuccess(false);
        return response;
    }

    @Override
    public String toString() {
        return "FeishuImageDownloadResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", success=" + success +
                ", data=" + data +
                '}';
    }
}
