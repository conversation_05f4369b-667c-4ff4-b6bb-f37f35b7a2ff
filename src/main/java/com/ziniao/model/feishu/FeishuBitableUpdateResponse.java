package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

/**
 * 飞书多维表格记录更新响应
 */
@ApiModel(description = "飞书多维表格记录更新响应")
public class FeishuBitableUpdateResponse {

    @ApiModelProperty(value = "响应码，0表示成功")
    private int code;

    @ApiModelProperty(value = "响应消息")
    private String msg;

    @ApiModelProperty(value = "响应数据")
    private Data data;

    /**
     * 响应数据
     */
    @ApiModel(description = "更新响应数据")
    public static class Data {
        @ApiModelProperty(value = "记录ID")
        private String recordId;

        @ApiModelProperty(value = "更新后的字段数据")
        private Map<String, Object> fields;

        @ApiModelProperty(value = "更新时间")
        private String updateTime;

        // Getters and Setters
        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public Map<String, Object> getFields() {
            return fields;
        }

        public void setFields(Map<String, Object> fields) {
            this.fields = fields;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }
    }

    // 便捷方法
    public static FeishuBitableUpdateResponse success(Data data) {
        FeishuBitableUpdateResponse response = new FeishuBitableUpdateResponse();
        response.setCode(0);
        response.setMsg("success");
        response.setData(data);
        return response;
    }

    public static FeishuBitableUpdateResponse error(int code, String message) {
        FeishuBitableUpdateResponse response = new FeishuBitableUpdateResponse();
        response.setCode(code);
        response.setMsg(message);
        return response;
    }

    public boolean isSuccess() {
        return code == 0;
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "FeishuBitableUpdateResponse{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
