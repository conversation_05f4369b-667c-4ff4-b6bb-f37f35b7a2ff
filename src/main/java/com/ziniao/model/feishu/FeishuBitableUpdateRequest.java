package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 飞书多维表格记录更新请求参数
 */
@ApiModel(description = "飞书多维表格记录更新请求参数")
public class FeishuBitableUpdateRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "MgDxby4r7avigssLQnVcIQzJnm1")
    @NotBlank(message = "app_token不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tbl4sH8PYHUk36K0")
    @NotBlank(message = "table_id不能为空")
    private String tableId;

    @ApiModelProperty(value = "记录的唯一标识符", required = true, example = "recqwIwhc6")
    @NotBlank(message = "record_id不能为空")
    private String recordId;

    @ApiModelProperty(value = "控制字段的返回格式", example = "user_id")
    private String userIdType = "user_id";

    @ApiModelProperty(value = "要更新的字段数据", required = true)
    private Map<String, Object> fields;

    // Getters and Setters
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getUserIdType() {
        return userIdType;
    }

    public void setUserIdType(String userIdType) {
        this.userIdType = userIdType;
    }

    public Map<String, Object> getFields() {
        return fields;
    }

    public void setFields(Map<String, Object> fields) {
        this.fields = fields;
    }

    @Override
    public String toString() {
        return "FeishuBitableUpdateRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", recordId='" + recordId + '\'' +
                ", userIdType='" + userIdType + '\'' +
                ", fields=" + fields +
                '}';
    }
}
