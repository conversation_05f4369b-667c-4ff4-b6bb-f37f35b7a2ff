package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 简化的飞书多维表格图片请求参数
 * 客户端只需要传递必要参数，服务器自动管理token
 */
@ApiModel(description = "简化的飞书多维表格图片请求参数")
public class SimpleFeishuImageRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "Wc2WwTiksil7vVkE1hqcmCmmneb")
    @NotBlank(message = "appToken不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tbl4sH8PYHUk36K0")
    @NotBlank(message = "tableId不能为空")
    private String tableId;

    @ApiModelProperty(value = "视图的唯一标识符，不传则使用默认视图", example = "vewgI30A6c")
    private String viewId;

    @ApiModelProperty(value = "记录ID，如果指定则只处理该记录", example = "recqwIwhc6")
    private String recordId;

    @ApiModelProperty(value = "图片字段名列表，如果不指定则自动识别所有图片字段", example = "[\"图片\", \"头像\", \"封面\"]")
    private List<String> imageFields;

    @ApiModelProperty(value = "是否下载图片到本地服务器", example = "true")
    private Boolean downloadToLocal = true;

    @ApiModelProperty(value = "筛选条件，用于筛选要处理的记录", example = "CurrentValue.[状态] = \"已发布\"")
    private String filter;

    @ApiModelProperty(value = "分页大小，最大值是 500", example = "20")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "分页标记，第一次请求不填", example = "recqwIwhc6")
    private String pageToken;

    @ApiModelProperty(value = "是否返回图片的详细信息", example = "true")
    private Boolean includeImageDetails = true;

    // Getters and Setters
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getViewId() {
        return viewId;
    }

    public void setViewId(String viewId) {
        this.viewId = viewId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public List<String> getImageFields() {
        return imageFields;
    }

    public void setImageFields(List<String> imageFields) {
        this.imageFields = imageFields;
    }

    public Boolean getDownloadToLocal() {
        return downloadToLocal;
    }

    public void setDownloadToLocal(Boolean downloadToLocal) {
        this.downloadToLocal = downloadToLocal;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }

    public Boolean getIncludeImageDetails() {
        return includeImageDetails;
    }

    public void setIncludeImageDetails(Boolean includeImageDetails) {
        this.includeImageDetails = includeImageDetails;
    }

    @Override
    public String toString() {
        return "SimpleFeishuImageRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", viewId='" + viewId + '\'' +
                ", recordId='" + recordId + '\'' +
                ", imageFields=" + imageFields +
                ", downloadToLocal=" + downloadToLocal +
                ", filter='" + filter + '\'' +
                ", pageSize=" + pageSize +
                ", pageToken='" + pageToken + '\'' +
                ", includeImageDetails=" + includeImageDetails +
                '}';
    }
}
