package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 飞书多维表格按记录ID字段映射图片处理响应
 * 支持轻量级响应、详细统计信息、错误处理等功能
 */
@ApiModel(description = "飞书多维表格按记录ID字段映射图片处理响应")
public class FeishuFieldMappingByRecordResponse {

    @ApiModelProperty(value = "响应状态码")
    private int code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "是否成功")
    private boolean success;

    @ApiModelProperty(value = "响应数据")
    private Data data;

    /**
     * 响应数据
     */
    @ApiModel(description = "响应数据")
    public static class Data {
        @ApiModelProperty(value = "处理ID")
        private String processId;

        @ApiModelProperty(value = "处理开始时间")
        private String startTime;

        @ApiModelProperty(value = "处理结束时间")
        private String endTime;

        @ApiModelProperty(value = "总处理时间（毫秒）")
        private long totalProcessingTime;

        @ApiModelProperty(value = "处理的记录总数")
        private int processedRecords;

        @ApiModelProperty(value = "找到的图片总数")
        private int totalImages;

        @ApiModelProperty(value = "成功下载的图片数")
        private int successfulDownloads;

        @ApiModelProperty(value = "下载失败的图片数")
        private int failedDownloads;

        @ApiModelProperty(value = "跳过的记录数（已有URL）")
        private int skippedRecords;

        @ApiModelProperty(value = "处理的页数")
        private int pagesProcessed;

        @ApiModelProperty(value = "是否还有更多数据")
        private boolean hasMoreData;

        @ApiModelProperty(value = "下一页的分页标记")
        private String nextPageToken;

        @ApiModelProperty(value = "是否因达到最大页数而停止")
        private boolean stoppedByMaxPages;

        @ApiModelProperty(value = "处理的记录列表（摘要模式时为空）")
        private List<RecordSummary> records;

        @ApiModelProperty(value = "错误信息列表")
        private List<ErrorInfo> errors;

        @ApiModelProperty(value = "性能统计")
        private PerformanceStats performanceStats;

        // Getters and Setters
        public String getProcessId() {
            return processId;
        }

        public void setProcessId(String processId) {
            this.processId = processId;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public long getTotalProcessingTime() {
            return totalProcessingTime;
        }

        public void setTotalProcessingTime(long totalProcessingTime) {
            this.totalProcessingTime = totalProcessingTime;
        }

        public int getProcessedRecords() {
            return processedRecords;
        }

        public void setProcessedRecords(int processedRecords) {
            this.processedRecords = processedRecords;
        }

        public int getTotalImages() {
            return totalImages;
        }

        public void setTotalImages(int totalImages) {
            this.totalImages = totalImages;
        }

        public int getSuccessfulDownloads() {
            return successfulDownloads;
        }

        public void setSuccessfulDownloads(int successfulDownloads) {
            this.successfulDownloads = successfulDownloads;
        }

        public int getFailedDownloads() {
            return failedDownloads;
        }

        public void setFailedDownloads(int failedDownloads) {
            this.failedDownloads = failedDownloads;
        }

        public int getSkippedRecords() {
            return skippedRecords;
        }

        public void setSkippedRecords(int skippedRecords) {
            this.skippedRecords = skippedRecords;
        }

        public int getPagesProcessed() {
            return pagesProcessed;
        }

        public void setPagesProcessed(int pagesProcessed) {
            this.pagesProcessed = pagesProcessed;
        }

        public boolean isHasMoreData() {
            return hasMoreData;
        }

        public void setHasMoreData(boolean hasMoreData) {
            this.hasMoreData = hasMoreData;
        }

        public String getNextPageToken() {
            return nextPageToken;
        }

        public void setNextPageToken(String nextPageToken) {
            this.nextPageToken = nextPageToken;
        }

        public boolean isStoppedByMaxPages() {
            return stoppedByMaxPages;
        }

        public void setStoppedByMaxPages(boolean stoppedByMaxPages) {
            this.stoppedByMaxPages = stoppedByMaxPages;
        }

        public List<RecordSummary> getRecords() {
            return records;
        }

        public void setRecords(List<RecordSummary> records) {
            this.records = records;
        }

        public List<ErrorInfo> getErrors() {
            return errors;
        }

        public void setErrors(List<ErrorInfo> errors) {
            this.errors = errors;
        }

        public PerformanceStats getPerformanceStats() {
            return performanceStats;
        }

        public void setPerformanceStats(PerformanceStats performanceStats) {
            this.performanceStats = performanceStats;
        }
    }

    /**
     * 记录摘要信息
     */
    @ApiModel(description = "记录摘要信息")
    public static class RecordSummary {
        @ApiModelProperty(value = "记录ID")
        private String recordId;

        @ApiModelProperty(value = "处理的字段数")
        private int processedFields;

        @ApiModelProperty(value = "该记录的图片总数")
        private int imageCount;

        @ApiModelProperty(value = "该记录成功下载的图片数")
        private int successCount;

        @ApiModelProperty(value = "该记录下载失败的图片数")
        private int failureCount;

        @ApiModelProperty(value = "处理状态")
        private String status;

        @ApiModelProperty(value = "错误信息（如果有）")
        private String errorMessage;

        @ApiModelProperty(value = "详细的图片信息（非摘要模式时包含）")
        private Map<String, List<FeishuImageDownloadResponse.ImageInfo>> imageDetails;

        // Getters and Setters
        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public int getProcessedFields() {
            return processedFields;
        }

        public void setProcessedFields(int processedFields) {
            this.processedFields = processedFields;
        }

        public int getImageCount() {
            return imageCount;
        }

        public void setImageCount(int imageCount) {
            this.imageCount = imageCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(int failureCount) {
            this.failureCount = failureCount;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Map<String, List<FeishuImageDownloadResponse.ImageInfo>> getImageDetails() {
            return imageDetails;
        }

        public void setImageDetails(Map<String, List<FeishuImageDownloadResponse.ImageInfo>> imageDetails) {
            this.imageDetails = imageDetails;
        }
    }

    /**
     * 错误信息
     */
    @ApiModel(description = "错误信息")
    public static class ErrorInfo {
        @ApiModelProperty(value = "错误类型")
        private String errorType;

        @ApiModelProperty(value = "错误消息")
        private String errorMessage;

        @ApiModelProperty(value = "相关记录ID")
        private String recordId;

        @ApiModelProperty(value = "相关字段名")
        private String fieldName;

        @ApiModelProperty(value = "错误发生时间")
        private String timestamp;

        // Getters and Setters
        public String getErrorType() {
            return errorType;
        }

        public void setErrorType(String errorType) {
            this.errorType = errorType;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }
    }

    /**
     * 性能统计
     */
    @ApiModel(description = "性能统计")
    public static class PerformanceStats {
        @ApiModelProperty(value = "平均每记录处理时间（毫秒）")
        private double avgProcessingTimePerRecord;

        @ApiModelProperty(value = "平均每图片下载时间（毫秒）")
        private double avgDownloadTimePerImage;

        @ApiModelProperty(value = "API调用次数")
        private int apiCallCount;

        @ApiModelProperty(value = "总下载字节数")
        private long totalDownloadBytes;

        // Getters and Setters
        public double getAvgProcessingTimePerRecord() {
            return avgProcessingTimePerRecord;
        }

        public void setAvgProcessingTimePerRecord(double avgProcessingTimePerRecord) {
            this.avgProcessingTimePerRecord = avgProcessingTimePerRecord;
        }

        public double getAvgDownloadTimePerImage() {
            return avgDownloadTimePerImage;
        }

        public void setAvgDownloadTimePerImage(double avgDownloadTimePerImage) {
            this.avgDownloadTimePerImage = avgDownloadTimePerImage;
        }

        public int getApiCallCount() {
            return apiCallCount;
        }

        public void setApiCallCount(int apiCallCount) {
            this.apiCallCount = apiCallCount;
        }

        public long getTotalDownloadBytes() {
            return totalDownloadBytes;
        }

        public void setTotalDownloadBytes(long totalDownloadBytes) {
            this.totalDownloadBytes = totalDownloadBytes;
        }
    }

    // Main class getters and setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    /**
     * 创建成功响应
     */
    public static FeishuFieldMappingByRecordResponse success(Data data) {
        FeishuFieldMappingByRecordResponse response = new FeishuFieldMappingByRecordResponse();
        response.setCode(200);
        response.setMessage("处理成功");
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    /**
     * 创建错误响应
     */
    public static FeishuFieldMappingByRecordResponse error(int code, String message) {
        FeishuFieldMappingByRecordResponse response = new FeishuFieldMappingByRecordResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setSuccess(false);
        return response;
    }

    @Override
    public String toString() {
        return "FeishuFieldMappingByRecordResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", success=" + success +
                ", data=" + data +
                '}';
    }
}
