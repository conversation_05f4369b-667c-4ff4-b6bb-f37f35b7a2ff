package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 飞书多维表格响应数据
 */
@ApiModel(description = "飞书多维表格响应数据")
public class FeishuBitableResponse {

    @ApiModelProperty(value = "错误码，非 0 表示失败")
    private int code;

    @ApiModelProperty(value = "错误描述")
    private String msg;

    @ApiModelProperty(value = "响应数据")
    private Data data;

    /**
     * 响应数据
     */
    @ApiModel(description = "响应数据")
    public static class Data {
        @ApiModelProperty(value = "是否还有更多项")
        private boolean hasMore;

        @ApiModelProperty(value = "分页标记，当 has_more 为 true 时，会同时返回新的 page_token")
        private String pageToken;

        @ApiModelProperty(value = "总数")
        private int total;

        @ApiModelProperty(value = "记录列表")
        private List<Record> items;

        // Getters and Setters
        public boolean isHasMore() {
            return hasMore;
        }

        public void setHasMore(boolean hasMore) {
            this.hasMore = hasMore;
        }

        public String getPageToken() {
            return pageToken;
        }

        public void setPageToken(String pageToken) {
            this.pageToken = pageToken;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public List<Record> getItems() {
            return items;
        }

        public void setItems(List<Record> items) {
            this.items = items;
        }
    }

    /**
     * 记录
     */
    @ApiModel(description = "记录")
    public static class Record {
        @ApiModelProperty(value = "记录ID")
        private String recordId;

        @ApiModelProperty(value = "创建人")
        private User createdBy;

        @ApiModelProperty(value = "创建时间")
        private long createdTime;

        @ApiModelProperty(value = "最后更新人")
        private User lastModifiedBy;

        @ApiModelProperty(value = "最后更新时间")
        private long lastModifiedTime;

        @ApiModelProperty(value = "记录字段")
        private Map<String, Object> fields;

        // Getters and Setters
        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public User getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(User createdBy) {
            this.createdBy = createdBy;
        }

        public long getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(long createdTime) {
            this.createdTime = createdTime;
        }

        public User getLastModifiedBy() {
            return lastModifiedBy;
        }

        public void setLastModifiedBy(User lastModifiedBy) {
            this.lastModifiedBy = lastModifiedBy;
        }

        public long getLastModifiedTime() {
            return lastModifiedTime;
        }

        public void setLastModifiedTime(long lastModifiedTime) {
            this.lastModifiedTime = lastModifiedTime;
        }

        public Map<String, Object> getFields() {
            return fields;
        }

        public void setFields(Map<String, Object> fields) {
            this.fields = fields;
        }
    }

    /**
     * 用户信息
     */
    @ApiModel(description = "用户信息")
    public static class User {
        @ApiModelProperty(value = "用户ID")
        private String id;

        @ApiModelProperty(value = "用户名")
        private String name;

        @ApiModelProperty(value = "用户邮箱")
        private String email;

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    /**
     * 附件信息
     */
    @ApiModel(description = "附件信息")
    public static class Attachment {
        @ApiModelProperty(value = "附件token")
        private String fileToken;

        @ApiModelProperty(value = "附件名")
        private String name;

        @ApiModelProperty(value = "附件类型")
        private String type;

        @ApiModelProperty(value = "附件大小")
        private long size;

        @ApiModelProperty(value = "附件URL")
        private String url;

        @ApiModelProperty(value = "临时URL")
        private String tmpUrl;

        // Getters and Setters
        public String getFileToken() {
            return fileToken;
        }

        public void setFileToken(String fileToken) {
            this.fileToken = fileToken;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public long getSize() {
            return size;
        }

        public void setSize(long size) {
            this.size = size;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getTmpUrl() {
            return tmpUrl;
        }

        public void setTmpUrl(String tmpUrl) {
            this.tmpUrl = tmpUrl;
        }
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return code == 0;
    }

    @Override
    public String toString() {
        return "FeishuBitableResponse{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
