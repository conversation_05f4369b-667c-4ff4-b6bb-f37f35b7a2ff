package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 飞书多维表格按记录ID字段映射图片处理请求参数
 * 支持精确记录ID处理、智能分页、轻量级响应等高级功能
 */
@ApiModel(description = "飞书多维表格按记录ID字段映射图片处理请求参数")
public class FeishuFieldMappingByRecordRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "MgDxby4r7avigssLQnVcIQzJnm1")
    @NotBlank(message = "app_token不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tbl4sH8PYHUk36K0")
    @NotBlank(message = "table_id不能为空")
    private String tableId;

    @ApiModelProperty(value = "视图的唯一标识符，不传则使用默认视图", example = "vewgI30A6c")
    private String viewId;

    @ApiModelProperty(value = "字段映射关系，key为源图片字段名，value为目标URL字段名", 
                     required = true,
                     example = "{\"👚上装正面图\": \"👚上装正面图url\", \"👚上装背面图\": \"👚上装背面图url\"}")
    @NotEmpty(message = "字段映射关系不能为空")
    private Map<String, String> fieldMapping;

    @ApiModelProperty(value = "要处理的记录ID列表，如果为空则处理所有记录", 
                     example = "[\"recqwIwhc6\", \"recABC123\", \"recXYZ789\"]")
    private List<String> recordIds;

    @ApiModelProperty(value = "是否启用智能分页处理大数据集", example = "true")
    private Boolean enableSmartPaging = false;

    @ApiModelProperty(value = "分页大小，最大值是 500", example = "200")
    private Integer pageSize = 200;

    @ApiModelProperty(value = "最大处理页数，0表示无限制", example = "10")
    private Integer maxPages = 0;

    @ApiModelProperty(value = "分页标记，用于继续上次的分页查询", example = "recqwIwhc6")
    private String pageToken;

    @ApiModelProperty(value = "是否只返回摘要信息，不包含详细的图片信息", example = "false")
    private Boolean summaryOnly = false;

    @ApiModelProperty(value = "图片下载超时时间（秒）", example = "30")
    private Integer downloadTimeout = 30;

    @ApiModelProperty(value = "最大并发下载数", example = "3")
    private Integer maxConcurrentDownloads = 3;

    @ApiModelProperty(value = "是否将本地URL写回多维表格", example = "true")
    private Boolean updateBitableWithLocalUrl = true;

    @ApiModelProperty(value = "遇到错误时是否继续处理其他记录", example = "true")
    private Boolean continueOnError = true;

    @ApiModelProperty(value = "是否跳过已有本地URL的记录", example = "false")
    private Boolean skipExistingUrls = false;

    @ApiModelProperty(value = "批处理大小，用于优化大量记录的处理", example = "50")
    private Integer batchSize = 50;

    @ApiModelProperty(value = "处理ID，用于日志追踪和问题排查", example = "process_20240131_001")
    private String processId;

    // Getters and Setters
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getViewId() {
        return viewId;
    }

    public void setViewId(String viewId) {
        this.viewId = viewId;
    }

    public Map<String, String> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(Map<String, String> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }

    public List<String> getRecordIds() {
        return recordIds;
    }

    public void setRecordIds(List<String> recordIds) {
        this.recordIds = recordIds;
    }

    public Boolean getEnableSmartPaging() {
        return enableSmartPaging;
    }

    public void setEnableSmartPaging(Boolean enableSmartPaging) {
        this.enableSmartPaging = enableSmartPaging;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getMaxPages() {
        return maxPages;
    }

    public void setMaxPages(Integer maxPages) {
        this.maxPages = maxPages;
    }

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }

    public Boolean getSummaryOnly() {
        return summaryOnly;
    }

    public void setSummaryOnly(Boolean summaryOnly) {
        this.summaryOnly = summaryOnly;
    }

    public Integer getDownloadTimeout() {
        return downloadTimeout;
    }

    public void setDownloadTimeout(Integer downloadTimeout) {
        this.downloadTimeout = downloadTimeout;
    }

    public Integer getMaxConcurrentDownloads() {
        return maxConcurrentDownloads;
    }

    public void setMaxConcurrentDownloads(Integer maxConcurrentDownloads) {
        this.maxConcurrentDownloads = maxConcurrentDownloads;
    }

    public Boolean getUpdateBitableWithLocalUrl() {
        return updateBitableWithLocalUrl;
    }

    public void setUpdateBitableWithLocalUrl(Boolean updateBitableWithLocalUrl) {
        this.updateBitableWithLocalUrl = updateBitableWithLocalUrl;
    }

    public Boolean getContinueOnError() {
        return continueOnError;
    }

    public void setContinueOnError(Boolean continueOnError) {
        this.continueOnError = continueOnError;
    }

    public Boolean getSkipExistingUrls() {
        return skipExistingUrls;
    }

    public void setSkipExistingUrls(Boolean skipExistingUrls) {
        this.skipExistingUrls = skipExistingUrls;
    }

    public Integer getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    @Override
    public String toString() {
        return "FeishuFieldMappingByRecordRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", viewId='" + viewId + '\'' +
                ", fieldMapping=" + fieldMapping +
                ", recordIds=" + recordIds +
                ", enableSmartPaging=" + enableSmartPaging +
                ", pageSize=" + pageSize +
                ", maxPages=" + maxPages +
                ", pageToken='" + pageToken + '\'' +
                ", summaryOnly=" + summaryOnly +
                ", downloadTimeout=" + downloadTimeout +
                ", maxConcurrentDownloads=" + maxConcurrentDownloads +
                ", updateBitableWithLocalUrl=" + updateBitableWithLocalUrl +
                ", continueOnError=" + continueOnError +
                ", skipExistingUrls=" + skipExistingUrls +
                ", batchSize=" + batchSize +
                ", processId='" + processId + '\'' +
                '}';
    }
}
