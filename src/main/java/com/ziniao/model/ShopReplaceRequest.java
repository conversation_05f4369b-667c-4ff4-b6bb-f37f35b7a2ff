package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * 商品替换请求参数（完全按照官方文档）
 */
@ApiModel(description = "商品替换请求参数")
public class ShopReplaceRequest {
    
    @JSONField(name = "imageUrl")
    @ApiModelProperty(value = "商品原图", required = true, example = "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/96a7a42fc965491dbc625d2db09f2478.png")
    @NotBlank(message = "商品原图不能为空")
    private String imageUrl;
    
    @JSONField(name = "sourceImageUrl")
    @ApiModelProperty(value = "原图抠出来的商品图")
    private String sourceImageUrl;
    
    @JSONField(name = "targetOriginUrl")
    @ApiModelProperty(value = "替换的目标原图", required = true, example = "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/9778678b4724467485346248032387ec.png")
    @NotBlank(message = "替换的目标原图不能为空")
    private String targetOriginUrl;
    
    @JSONField(name = "targetImageUrl")
    @ApiModelProperty(value = "替换的目标原图抠图结果")
    private String targetImageUrl;
    
    @JSONField(name = "denoiseStrength")
    @ApiModelProperty(value = "生成图变化程度 默认值：0.5 越大越高 浮点数 可选范围 [0,1]", example = "0.5")
    private String denoiseStrength;
    
    @JSONField(name = "imageOutputWidth")
    @ApiModelProperty(value = "输出尺寸 宽度 最长边不能超过4096,最短边不能低于32")
    private Integer imageOutputWidth;
    
    @JSONField(name = "imageOutputHeight")
    @ApiModelProperty(value = "输出尺寸 高度 最长边不能超过4096,最短边不能低于32")
    private Integer imageOutputHeight;
    
    @JSONField(name = "outputNum")
    @ApiModelProperty(value = "输出张数 取值范围：[1,4] 默认值：1", example = "1")
    private Integer outputNum;
    
    public ShopReplaceRequest() {
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getSourceImageUrl() {
        return sourceImageUrl;
    }
    
    public void setSourceImageUrl(String sourceImageUrl) {
        this.sourceImageUrl = sourceImageUrl;
    }
    
    public String getTargetOriginUrl() {
        return targetOriginUrl;
    }
    
    public void setTargetOriginUrl(String targetOriginUrl) {
        this.targetOriginUrl = targetOriginUrl;
    }
    
    public String getTargetImageUrl() {
        return targetImageUrl;
    }
    
    public void setTargetImageUrl(String targetImageUrl) {
        this.targetImageUrl = targetImageUrl;
    }
    
    public String getDenoiseStrength() {
        return denoiseStrength;
    }
    
    public void setDenoiseStrength(String denoiseStrength) {
        this.denoiseStrength = denoiseStrength;
    }
    
    public Integer getImageOutputWidth() {
        return imageOutputWidth;
    }
    
    public void setImageOutputWidth(Integer imageOutputWidth) {
        this.imageOutputWidth = imageOutputWidth;
    }
    
    public Integer getImageOutputHeight() {
        return imageOutputHeight;
    }
    
    public void setImageOutputHeight(Integer imageOutputHeight) {
        this.imageOutputHeight = imageOutputHeight;
    }
    
    public Integer getOutputNum() {
        return outputNum;
    }
    
    public void setOutputNum(Integer outputNum) {
        this.outputNum = outputNum;
    }
    
    @Override
    public String toString() {
        return "ShopReplaceRequest{" +
                "imageUrl='" + imageUrl + '\'' +
                ", sourceImageUrl='" + sourceImageUrl + '\'' +
                ", targetOriginUrl='" + targetOriginUrl + '\'' +
                ", targetImageUrl='" + targetImageUrl + '\'' +
                ", denoiseStrength='" + denoiseStrength + '\'' +
                ", imageOutputWidth=" + imageOutputWidth +
                ", imageOutputHeight=" + imageOutputHeight +
                ", outputNum=" + outputNum +
                '}';
    }
}
