package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * AI穿衣-连体衣响应（完全按照官方文档）
 */
@ApiModel(description = "AI穿衣-连体衣响应")
public class FittingRoomSuitResponse {
    
    @JSONField(name = "request_id")
    @ApiModelProperty(value = "全局请求id，方便追踪定位")
    private String requestId;
    
    @JSONField(name = "code")
    @ApiModelProperty(value = "网关返回码，0表示成功")
    private String code;
    
    @JSONField(name = "msg")
    @ApiModelProperty(value = "网关返回码描述")
    private String msg;
    
    @JSONField(name = "sub_code")
    @ApiModelProperty(value = "业务返回码，成功时不返回")
    private String subCode;
    
    @JSONField(name = "sub_msg")
    @ApiModelProperty(value = "业务返回码描述，成功时不返回")
    private String subMsg;
    
    @JSONField(name = "data")
    @ApiModelProperty(value = "业务响应参数")
    private ResponseData data;

    @JSONField(name = "success")
    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @JSONField(name = "task_id")
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    
    /**
     * 响应数据
     */
    @ApiModel(description = "响应数据")
    public static class ResponseData {
        
        @JSONField(name = "msg")
        @ApiModelProperty(value = "消息")
        private String msg;
        
        @JSONField(name = "code")
        @ApiModelProperty(value = "状态码")
        private Integer code;
        
        @JSONField(name = "traceId")
        @ApiModelProperty(value = "链路ID")
        private String traceId;
        
        @JSONField(name = "data")
        @ApiModelProperty(value = "数据")
        private TaskData data;
        
        @JSONField(name = "msgKey")
        @ApiModelProperty(value = "消息键")
        private String msgKey;
        
        public String getMsg() {
            return msg;
        }
        
        public void setMsg(String msg) {
            this.msg = msg;
        }
        
        public Integer getCode() {
            return code;
        }
        
        public void setCode(Integer code) {
            this.code = code;
        }
        
        public String getTraceId() {
            return traceId;
        }
        
        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }
        
        public TaskData getData() {
            return data;
        }
        
        public void setData(TaskData data) {
            this.data = data;
        }
        
        public String getMsgKey() {
            return msgKey;
        }
        
        public void setMsgKey(String msgKey) {
            this.msgKey = msgKey;
        }
    }
    
    /**
     * 任务数据
     */
    @ApiModel(description = "任务数据")
    public static class TaskData {
        
        @JSONField(name = "id")
        @ApiModelProperty(value = "任务ID")
        private String id;
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
    }
    
    public FittingRoomSuitResponse() {
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public String getSubCode() {
        return subCode;
    }
    
    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }
    
    public String getSubMsg() {
        return subMsg;
    }
    
    public void setSubMsg(String subMsg) {
        this.subMsg = subMsg;
    }
    
    public ResponseData getData() {
        return data;
    }
    
    public void setData(ResponseData data) {
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        // 如果有显式的success字段，优先使用
        if (success != null) {
            return success;
        }
        // 否则使用原来的逻辑
        return "0".equals(code) && data != null && data.getCode() != null && data.getCode() == 200;
    }
    
    /**
     * 获取任务ID
     */
    public String getTaskId() {
        if (data != null && data.getData() != null) {
            return data.getData().getId();
        }
        return null;
    }
}
