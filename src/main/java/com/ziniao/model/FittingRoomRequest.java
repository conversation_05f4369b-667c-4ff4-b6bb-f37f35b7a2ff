package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * AI穿衣请求参数（完全按照官方文档）
 */
@ApiModel(description = "AI穿衣请求参数")
public class FittingRoomRequest {
    
    @JSONField(name = "upperOriginUrl")
    @ApiModelProperty(value = "上衣原图，上衣和下衣必传其一", example = "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp")
    private String upperOriginUrl;
    
    @JSONField(name = "upperImageUrl")
    @ApiModelProperty(value = "上衣抠图结果")
    private String upperImageUrl;
    
    @JSONField(name = "downOriginUrl")
    @ApiModelProperty(value = "下装原图，上衣和下衣必传其一", example = "https://cdn.linkfox.com/ai-site/test/workbench/down-true3.jpg")
    private String downOriginUrl;
    
    @JSONField(name = "downImageUrl")
    @ApiModelProperty(value = "下装抠图结果")
    private String downImageUrl;
    
    @JSONField(name = "modelImageUrl")
    @ApiModelProperty(value = "模特姿势图", required = true, example = "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png")
    private String modelImageUrl;
    
    @JSONField(name = "modelMaskImageUrl")
    @ApiModelProperty(value = "模特姿势图穿戴区域图，黑白图，黑色为穿戴区域")
    private String modelMaskImageUrl;
    
    @JSONField(name = "outputNum")
    @ApiModelProperty(value = "输出张数，取值范围：[1,4]，默认值：1")
    private Integer outputNum;
    
    public FittingRoomRequest() {
    }
    
    public String getUpperOriginUrl() {
        return upperOriginUrl;
    }
    
    public void setUpperOriginUrl(String upperOriginUrl) {
        this.upperOriginUrl = upperOriginUrl;
    }
    
    public String getUpperImageUrl() {
        return upperImageUrl;
    }
    
    public void setUpperImageUrl(String upperImageUrl) {
        this.upperImageUrl = upperImageUrl;
    }
    
    public String getDownOriginUrl() {
        return downOriginUrl;
    }
    
    public void setDownOriginUrl(String downOriginUrl) {
        this.downOriginUrl = downOriginUrl;
    }
    
    public String getDownImageUrl() {
        return downImageUrl;
    }
    
    public void setDownImageUrl(String downImageUrl) {
        this.downImageUrl = downImageUrl;
    }
    
    public String getModelImageUrl() {
        return modelImageUrl;
    }
    
    public void setModelImageUrl(String modelImageUrl) {
        this.modelImageUrl = modelImageUrl;
    }
    
    public String getModelMaskImageUrl() {
        return modelMaskImageUrl;
    }
    
    public void setModelMaskImageUrl(String modelMaskImageUrl) {
        this.modelMaskImageUrl = modelMaskImageUrl;
    }
    
    public Integer getOutputNum() {
        return outputNum;
    }
    
    public void setOutputNum(Integer outputNum) {
        this.outputNum = outputNum;
    }
    
    @Override
    public String toString() {
        return "FittingRoomRequest{" +
                "upperOriginUrl='" + upperOriginUrl + '\'' +
                ", upperImageUrl='" + upperImageUrl + '\'' +
                ", downOriginUrl='" + downOriginUrl + '\'' +
                ", downImageUrl='" + downImageUrl + '\'' +
                ", modelImageUrl='" + modelImageUrl + '\'' +
                ", modelMaskImageUrl='" + modelMaskImageUrl + '\'' +
                ", outputNum=" + outputNum +
                '}';
    }
}
