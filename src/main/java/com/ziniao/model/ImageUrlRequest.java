package com.ziniao.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * 图片URL下载请求模型
 */
@ApiModel(description = "图片URL下载请求")
public class ImageUrlRequest {

    @ApiModelProperty(value = "图片URL地址", required = true, example = "https://example.com/image.jpg")
    @NotBlank(message = "图片URL不能为空")
    private String imageUrl;

    @ApiModelProperty(value = "自定义文件名（可选）", example = "my_image")
    private String customFileName;

    // 构造函数
    public ImageUrlRequest() {}

    public ImageUrlRequest(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public ImageUrlRequest(String imageUrl, String customFileName) {
        this.imageUrl = imageUrl;
        this.customFileName = customFileName;
    }

    // Getters and Setters
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getCustomFileName() {
        return customFileName;
    }

    public void setCustomFileName(String customFileName) {
        this.customFileName = customFileName;
    }

    @Override
    public String toString() {
        return "ImageUrlRequest{" +
                "imageUrl='" + imageUrl + '\'' +
                ", customFileName='" + customFileName + '\'' +
                '}';
    }
}
