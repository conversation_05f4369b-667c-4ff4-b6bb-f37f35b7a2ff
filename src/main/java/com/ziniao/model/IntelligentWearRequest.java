package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * AI穿戴请求参数（完全按照官方文档）
 */
@ApiModel(description = "AI穿戴请求参数")
public class IntelligentWearRequest {

    @JSONField(name = "imageUrl")
    @ApiModelProperty(value = "穿戴商品图", required = true, example = "https://example.com/product.jpg")
    @NotBlank(message = "穿戴商品图不能为空")
    private String imageUrl;

    @JSONField(name = "targetOriginUrl")
    @ApiModelProperty(value = "穿戴模特原图 1. 图片地址 2. 调用作图素材取地址(type=7)", required = true, example = "https://example.com/model.jpg")
    @NotBlank(message = "穿戴模特原图不能为空")
    private String targetOriginUrl;

    @JSONField(name = "targetMaskUrl")
    @ApiModelProperty(value = "穿戴模特穿戴区域图片 targetOriginUrl为自定义图片地址时 必填（黑白图尺寸与原图一致）", required = false, example = "https://example.com/mask.jpg")
    private String targetMaskUrl;

    @JSONField(name = "outputNum")
    @ApiModelProperty(value = "输出张数 取值范围：[1,4] 默认值：1", required = false, example = "1")
    private Integer outputNum;

    public IntelligentWearRequest() {
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getTargetOriginUrl() {
        return targetOriginUrl;
    }

    public void setTargetOriginUrl(String targetOriginUrl) {
        this.targetOriginUrl = targetOriginUrl;
    }

    public String getTargetMaskUrl() {
        return targetMaskUrl;
    }

    public void setTargetMaskUrl(String targetMaskUrl) {
        this.targetMaskUrl = targetMaskUrl;
    }

    public Integer getOutputNum() {
        return outputNum;
    }

    public void setOutputNum(Integer outputNum) {
        this.outputNum = outputNum;
    }

    @Override
    public String toString() {
        return "IntelligentWearRequest{" +
                "imageUrl='" + imageUrl + '\'' +
                ", targetOriginUrl='" + targetOriginUrl + '\'' +
                ", targetMaskUrl='" + targetMaskUrl + '\'' +
                ", outputNum=" + outputNum +
                '}';
    }
}
