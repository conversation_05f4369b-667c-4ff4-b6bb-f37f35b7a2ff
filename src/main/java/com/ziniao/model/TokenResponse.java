package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 令牌响应模型
 */
public class TokenResponse {
    
    @JSONField(name = "appAuthToken")
    private String appAuthToken;
    
    @JSONField(name = "expiresIn")
    private Long expiresIn;
    
    public TokenResponse() {
    }
    
    public TokenResponse(String appAuthToken, Long expiresIn) {
        this.appAuthToken = appAuthToken;
        this.expiresIn = expiresIn;
    }
    
    public String getAppAuthToken() {
        return appAuthToken;
    }
    
    public void setAppAuthToken(String appAuthToken) {
        this.appAuthToken = appAuthToken;
    }
    
    public Long getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    public boolean isValid() {
        return appAuthToken != null && !appAuthToken.isEmpty();
    }
    
    @Override
    public String toString() {
        return "TokenResponse{" +
                "appAuthToken='" + appAuthToken + '\'' +
                ", expiresIn=" + expiresIn +
                '}';
    }
}
