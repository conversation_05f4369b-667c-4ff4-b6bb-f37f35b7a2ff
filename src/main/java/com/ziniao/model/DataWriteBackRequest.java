package com.ziniao.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 数据回写请求参数
 */
@ApiModel(description = "数据回写请求参数")
public class DataWriteBackRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "MgDxby4r7avigssLQnVcIQzJnm1")
    @NotBlank(message = "app_token不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tbl4sH8PYHUk36K0")
    @NotBlank(message = "table_id不能为空")
    private String tableId;

    @ApiModelProperty(value = "记录的唯一标识符", required = true, example = "recqwIwhc6")
    @NotBlank(message = "record_id不能为空")
    private String recordId;

    @ApiModelProperty(value = "要回写的字段数据", required = true, 
                     notes = "键为字段名，值为要写入的数据。支持文本、数字、图片URL等类型")
    @NotNull(message = "回写数据不能为空")
    private Map<String, Object> fields;

    @ApiModelProperty(value = "任务ID，用于关联AI处理任务", example = "1945877291232894976")
    private String taskId;

    @ApiModelProperty(value = "回写类型", example = "ai_result", 
                     allowableValues = "ai_result,manual_update,batch_update")
    private String writeBackType = "ai_result";

    @ApiModelProperty(value = "备注信息", example = "AI穿衣结果回写")
    private String remark;

    // 构造函数
    public DataWriteBackRequest() {
    }

    // Getters and Setters
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public Map<String, Object> getFields() {
        return fields;
    }

    public void setFields(Map<String, Object> fields) {
        this.fields = fields;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getWriteBackType() {
        return writeBackType;
    }

    public void setWriteBackType(String writeBackType) {
        this.writeBackType = writeBackType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "DataWriteBackRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", recordId='" + recordId + '\'' +
                ", fields=" + fields +
                ", taskId='" + taskId + '\'' +
                ", writeBackType='" + writeBackType + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
