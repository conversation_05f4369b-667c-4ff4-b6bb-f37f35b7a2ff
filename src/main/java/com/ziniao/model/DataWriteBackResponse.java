package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

/**
 * 数据回写响应结果
 */
@ApiModel(description = "数据回写响应结果")
public class DataWriteBackResponse {

    @JSONField(name = "code")
    @ApiModelProperty(value = "响应状态码", example = "200")
    private int code;

    @JSONField(name = "message")
    @ApiModelProperty(value = "响应消息", example = "数据回写成功")
    private String message;

    @JSONField(name = "data")
    @ApiModelProperty(value = "响应数据")
    private WriteBackData data;

    @JSONField(name = "request_id")
    @ApiModelProperty(value = "请求ID")
    private String requestId;

    // 构造函数
    public DataWriteBackResponse() {
    }

    // 静态工厂方法
    public static DataWriteBackResponse success(WriteBackData data) {
        DataWriteBackResponse response = new DataWriteBackResponse();
        response.setCode(200);
        response.setMessage("数据回写成功");
        response.setData(data);
        return response;
    }

    public static DataWriteBackResponse error(int code, String message) {
        DataWriteBackResponse response = new DataWriteBackResponse();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    // 判断是否成功
    public boolean isSuccess() {
        return code == 200;
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public WriteBackData getData() {
        return data;
    }

    public void setData(WriteBackData data) {
        this.data = data;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public String toString() {
        return "DataWriteBackResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", requestId='" + requestId + '\'' +
                '}';
    }

    /**
     * 回写数据内部类
     */
    @ApiModel(description = "回写数据")
    public static class WriteBackData {

        @JSONField(name = "record_id")
        @ApiModelProperty(value = "记录ID", example = "recqwIwhc6")
        private String recordId;

        @JSONField(name = "task_id")
        @ApiModelProperty(value = "任务ID", example = "1945877291232894976")
        private String taskId;

        @JSONField(name = "updated_fields")
        @ApiModelProperty(value = "已更新的字段")
        private Map<String, Object> updatedFields;

        @JSONField(name = "update_time")
        @ApiModelProperty(value = "更新时间", example = "2024-01-15 10:30:00")
        private String updateTime;

        @JSONField(name = "write_back_type")
        @ApiModelProperty(value = "回写类型", example = "ai_result")
        private String writeBackType;

        @JSONField(name = "remark")
        @ApiModelProperty(value = "备注信息", example = "AI穿衣结果回写")
        private String remark;

        // 构造函数
        public WriteBackData() {
        }

        // Getters and Setters
        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public Map<String, Object> getUpdatedFields() {
            return updatedFields;
        }

        public void setUpdatedFields(Map<String, Object> updatedFields) {
            this.updatedFields = updatedFields;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getWriteBackType() {
            return writeBackType;
        }

        public void setWriteBackType(String writeBackType) {
            this.writeBackType = writeBackType;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        @Override
        public String toString() {
            return "WriteBackData{" +
                    "recordId='" + recordId + '\'' +
                    ", taskId='" + taskId + '\'' +
                    ", updatedFields=" + updatedFields +
                    ", updateTime='" + updateTime + '\'' +
                    ", writeBackType='" + writeBackType + '\'' +
                    ", remark='" + remark + '\'' +
                    '}';
        }
    }
}
