package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * AI穿衣-连体衣请求参数（完全按照官方文档）
 */
@ApiModel(description = "AI穿衣-连体衣请求参数")
public class FittingRoomSuitRequest {

    @JSONField(name = "suitOriginUrl")
    @ApiModelProperty(value = "连体衣原图", required = true, example = "https://example.com/suit.jpg")
    @NotBlank(message = "连体衣原图不能为空")
    private String suitOriginUrl;

    @JSONField(name = "suitImageUrl")
    @ApiModelProperty(value = "连体衣抠图结果", required = false, example = "https://example.com/suit_mask.jpg")
    private String suitImageUrl;

    @JSONField(name = "modelImageUrl")
    @ApiModelProperty(value = "模特姿势图 1. 图片地址 2. 调用作图素材取地址(type=6)", required = true, example = "https://example.com/model.jpg")
    @NotBlank(message = "模特姿势图不能为空")
    private String modelImageUrl;

    @JSONField(name = "modelMaskImageUrl")
    @ApiModelProperty(value = "模特姿势图穿戴区域图", required = false, example = "https://example.com/model_mask.jpg")
    private String modelMaskImageUrl;

    @JSONField(name = "outputNum")
    @ApiModelProperty(value = "输出张数 取值范围：[1,4] 默认值：1", required = false, example = "1")
    private Integer outputNum;

    public FittingRoomSuitRequest() {
    }

    public String getSuitOriginUrl() {
        return suitOriginUrl;
    }

    public void setSuitOriginUrl(String suitOriginUrl) {
        this.suitOriginUrl = suitOriginUrl;
    }

    public String getSuitImageUrl() {
        return suitImageUrl;
    }

    public void setSuitImageUrl(String suitImageUrl) {
        this.suitImageUrl = suitImageUrl;
    }

    public String getModelImageUrl() {
        return modelImageUrl;
    }

    public void setModelImageUrl(String modelImageUrl) {
        this.modelImageUrl = modelImageUrl;
    }

    public String getModelMaskImageUrl() {
        return modelMaskImageUrl;
    }

    public void setModelMaskImageUrl(String modelMaskImageUrl) {
        this.modelMaskImageUrl = modelMaskImageUrl;
    }

    public Integer getOutputNum() {
        return outputNum;
    }

    public void setOutputNum(Integer outputNum) {
        this.outputNum = outputNum;
    }

    @Override
    public String toString() {
        return "FittingRoomSuitRequest{" +
                "suitOriginUrl='" + suitOriginUrl + '\'' +
                ", suitImageUrl='" + suitImageUrl + '\'' +
                ", modelImageUrl='" + modelImageUrl + '\'' +
                ", modelMaskImageUrl='" + modelMaskImageUrl + '\'' +
                ", outputNum=" + outputNum +
                '}';
    }
}
