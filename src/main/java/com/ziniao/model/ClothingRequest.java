package com.ziniao.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * AI穿衣请求参数
 */
@ApiModel(description = "AI穿衣请求参数")
public class ClothingRequest {

    @JSONField(name = "person_image")
    @ApiModelProperty(value = "人物图片URL或base64编码", required = true, example = "https://example.com/person.jpg")
    @NotBlank(message = "人物图片不能为空")
    private String personImage;

    @JSONField(name = "clothing_image")
    @ApiModelProperty(value = "服装图片URL或base64编码", required = true, example = "https://example.com/clothing.jpg")
    @NotBlank(message = "服装图片不能为空")
    private String clothingImage;

    @JSONField(name = "clothing_type")
    @ApiModelProperty(value = "服装类型", required = true, example = "上衣", allowableValues = "上衣,下装")
    @NotBlank(message = "服装类型不能为空")
    private String clothingType;

    @JSONField(name = "style")
    @ApiModelProperty(value = "风格参数", example = "casual", allowableValues = "casual,formal,sport")
    private String style;

    @JSONField(name = "quality")
    @ApiModelProperty(value = "质量参数", example = "high", allowableValues = "low,medium,high")
    private String quality;
    
    public ClothingRequest() {
    }
    
    public ClothingRequest(String personImage, String clothingImage, String clothingType) {
        this.personImage = personImage;
        this.clothingImage = clothingImage;
        this.clothingType = clothingType;
    }
    
    // Getters and Setters
    public String getPersonImage() {
        return personImage;
    }
    
    public void setPersonImage(String personImage) {
        this.personImage = personImage;
    }
    
    public String getClothingImage() {
        return clothingImage;
    }
    
    public void setClothingImage(String clothingImage) {
        this.clothingImage = clothingImage;
    }
    
    public String getClothingType() {
        return clothingType;
    }
    
    public void setClothingType(String clothingType) {
        this.clothingType = clothingType;
    }
    
    public String getStyle() {
        return style;
    }
    
    public void setStyle(String style) {
        this.style = style;
    }
    
    public String getQuality() {
        return quality;
    }
    
    public void setQuality(String quality) {
        this.quality = quality;
    }
    
    @Override
    public String toString() {
        return "ClothingRequest{" +
                "personImage='" + personImage + '\'' +
                ", clothingImage='" + clothingImage + '\'' +
                ", clothingType='" + clothingType + '\'' +
                ", style='" + style + '\'' +
                ", quality='" + quality + '\'' +
                '}';
    }
}
