package com.ziniao;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;

/**
 * 紫鸟AI穿衣Demo应用主类
 */
@SpringBootApplication
@EnableConfigurationProperties
@EnableCaching
public class ZiniaoAiDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZiniaoAiDemoApplication.class, args);
        System.out.println("=== 紫鸟AI穿衣Demo应用启动成功 ===");
        System.out.println("API文档地址: http://localhost:8080/doc.html");
        System.out.println("Swagger UI: http://localhost:8080/swagger-ui/");
    }
}
