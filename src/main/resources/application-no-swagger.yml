server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: ziniao-ai-demo
  
  # 缓存配置
  cache:
    type: simple
    cache-names:
      - appToken

# 紫鸟API配置
ziniao:
  api:
    base-url: https://sbappstoreapi.ziniao.com
    app-id: your_app_id_here
    private-key: your_private_key_here
    timeout: 30000
    token-cache-time: 7200

# 日志配置
logging:
  level:
    com.ziniao: DEBUG
    com.fzzixun: INFO
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ziniao-ai-demo.log
    max-size: 10MB
    max-history: 30
