#!/bin/bash

# 所有接口综合测试脚本

# 设置服务器地址
SERVER_URL="http://localhost:8080"

echo "=== 紫鸟AI接口综合测试 ==="
echo "服务器地址: $SERVER_URL"
echo ""

# 1. 测试健康检查
echo "1. 测试健康检查接口"
HEALTH_RESPONSE=$(curl -s -X GET "$SERVER_URL/api/clothing/health")
echo "健康检查响应: $HEALTH_RESPONSE"
echo ""

# 2. 测试AI穿衣接口
echo "2. 测试AI穿衣接口 (FittingRoom)"
FITTING_REQUEST='{
  "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/96a7a42fc965491dbc625d2db09f2478.png",
  "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/9778678b4724467485346248032387ec.png",
  "outputNum": 1
}'

FITTING_RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/fittingRoom" \
  -H "Content-Type: application/json" \
  -d "$FITTING_REQUEST")

echo "AI穿衣响应: $FITTING_RESPONSE"

if echo "$FITTING_RESPONSE" | grep -q '"success":true'; then
    echo "✅ AI穿衣接口测试成功"
else
    echo "❌ AI穿衣接口测试失败"
fi
echo ""

# 3. 测试商品替换接口
echo "3. 测试商品替换接口 (ShopReplace)"
SHOP_REPLACE_REQUEST='{
  "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/96a7a42fc965491dbc625d2db09f2478.png",
  "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/9778678b4724467485346248032387ec.png",
  "denoiseStrength": "0.5",
  "outputNum": 1
}'

SHOP_REPLACE_RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/shopReplace" \
  -H "Content-Type: application/json" \
  -d "$SHOP_REPLACE_REQUEST")

echo "商品替换响应: $SHOP_REPLACE_RESPONSE"

if echo "$SHOP_REPLACE_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 商品替换接口测试成功"
else
    echo "❌ 商品替换接口测试失败"
fi
echo ""

# 4. 测试AI穿衣-连体衣接口
echo "4. 测试AI穿衣-连体衣接口 (FittingRoomSuit)"
FITTING_ROOM_SUIT_REQUEST='{
  "suitOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
  "suitImageUrl": "",
  "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
  "modelMaskImageUrl": "",
  "outputNum": 1
}'

FITTING_ROOM_SUIT_RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/fittingRoomSuit" \
  -H "Content-Type: application/json" \
  -d "$FITTING_ROOM_SUIT_REQUEST")

echo "AI穿衣-连体衣响应: $FITTING_ROOM_SUIT_RESPONSE"

if echo "$FITTING_ROOM_SUIT_RESPONSE" | grep -q '"success":true'; then
    echo "✅ AI穿衣-连体衣接口测试成功"
else
    echo "❌ AI穿衣-连体衣接口测试失败"
fi
echo ""

# 5. 测试AI穿戴接口
echo "5. 测试AI穿戴接口 (IntelligentWear)"
INTELLIGENT_WEAR_REQUEST='{
  "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/06/f8703b6b802c4c6c83fad152e2a7c07d.webp",
  "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/06/10dbbb7649a5445c87184ab287ed3b4b.png",
  "targetMaskUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/16/3ce9190e4ce64f2f88406d36409ffc55.png",
  "outputNum": 1
}'

INTELLIGENT_WEAR_RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/intelligentWear" \
  -H "Content-Type: application/json" \
  -d "$INTELLIGENT_WEAR_REQUEST")

echo "AI穿戴响应: $INTELLIGENT_WEAR_RESPONSE"

if echo "$INTELLIGENT_WEAR_RESPONSE" | grep -q '"success":true'; then
    echo "✅ AI穿戴接口测试成功"
else
    echo "❌ AI穿戴接口测试失败"
fi
echo ""

# 6. 测试令牌接口
echo "6. 测试令牌接口"
TOKEN_RESPONSE=$(curl -s -X GET "$SERVER_URL/api/token/app")
echo "令牌响应: $TOKEN_RESPONSE"

if echo "$TOKEN_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 令牌接口测试成功"
else
    echo "❌ 令牌接口测试失败"
fi
echo ""

echo "=== 测试总结 ==="
echo "所有接口测试完成！"
echo ""
echo "可用的接口："
echo "- GET  /api/clothing/health           - 健康检查"
echo "- POST /api/clothing/fittingRoom      - AI穿衣"
echo "- POST /api/clothing/shopReplace      - 商品替换"
echo "- POST /api/clothing/fittingRoomSuit  - AI穿衣-连体衣"
echo "- POST /api/clothing/intelligentWear  - AI穿戴"
echo "- GET  /api/clothing/result/{taskId}  - 查询任务结果"
echo "- GET  /api/token/app                 - 获取应用令牌"
echo ""
echo "API文档地址: $SERVER_URL/doc.html"
echo "Swagger UI: $SERVER_URL/swagger-ui/"
