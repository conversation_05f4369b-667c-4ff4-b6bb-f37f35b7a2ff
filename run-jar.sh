#!/bin/bash

# 紫鸟AI穿衣API JAR运行脚本

JAR_FILE="target/ziniao-ai-demo-1.0.0.jar"
LOG_FILE="logs/application.log"

echo "=== 紫鸟AI穿衣API 启动脚本 ==="
echo ""

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "❌ 错误: 找不到JAR文件 $JAR_FILE"
    echo "请确保已经运行 mvn clean package 构建项目"
    exit 1
fi

echo "✅ 找到JAR文件: $JAR_FILE"

# 创建日志目录
mkdir -p logs

echo ""
echo "启动应用..."
echo "JAR文件: $JAR_FILE"
echo "日志文件: $LOG_FILE"
echo ""

# 启动应用
java -jar "$JAR_FILE" 2>&1 | tee "$LOG_FILE"
