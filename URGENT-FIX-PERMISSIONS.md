# 🚨 紧急修复：飞书应用权限配置

## 🎯 问题确认
你的应用 `cli_a8fe3e73bd78d00d` 缺少访问多维表格的权限，导致 91402 NOTEXIST 错误。

## 🔧 立即解决步骤

### 步骤1: 访问应用管理页面
**直接链接**: https://open.feishu.cn/app/cli_a8fe3e73bd78d00d

### 步骤2: 添加权限
在应用管理页面，找到"权限管理"或"Permissions"，添加以下权限：

#### 必需权限 (Required)
```
bitable:app                    # 获取多维表格信息
bitable:app:readonly           # 读取多维表格数据
wiki:wiki:readonly             # 读取知识库内容（你的表格在知识库中）
```

#### 可选权限 (Optional)
```
drive:drive:readonly           # 下载图片文件
```

### 步骤3: 权限申请链接
如果找不到权限配置页面，直接访问这些链接申请权限：

**多维表格权限**:
https://open.feishu.cn/app/cli_a8fe3e73bd78d00d/auth?q=bitable:app,bitable:app:readonly

**知识库权限**:
https://open.feishu.cn/app/cli_a8fe3e73bd78d00d/auth?q=wiki:wiki:readonly

### 步骤4: 发布权限
1. 保存权限配置
2. 找到"版本管理"或"Version Management"
3. 创建新版本
4. 发布应用

### 步骤5: 重新安装应用（如果需要）
如果是企业应用，可能需要重新安装到组织中。

## 🧪 验证权限配置

权限配置完成后，运行以下命令验证：

```bash
# 1. 运行诊断脚本
./diagnose-bitable-access.sh

# 2. 测试基础API
curl -X POST "http://localhost:8080/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "pageSize": 3
  }'

# 3. 测试图片获取API
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "pageSize": 5
  }'
```

## 📋 权限配置检查清单

- [ ] 访问应用管理页面
- [ ] 添加 `bitable:app` 权限
- [ ] 添加 `bitable:app:readonly` 权限  
- [ ] 添加 `wiki:wiki:readonly` 权限
- [ ] 保存权限配置
- [ ] 发布新版本
- [ ] 等待权限生效（通常几分钟）
- [ ] 运行验证测试

## ⚠️ 常见问题

### Q: 找不到权限配置页面？
A: 直接使用上面提供的权限申请链接

### Q: 权限添加后仍然报错？
A: 
1. 确保权限已发布（不只是保存）
2. 等待5-10分钟让权限生效
3. 清除应用令牌缓存（重启服务）

### Q: 企业应用特殊处理？
A: 企业应用可能需要管理员审批，联系你的飞书管理员

## 🎯 成功标志

权限配置成功后，你应该看到：
- ✅ 诊断脚本显示多维表格信息获取成功
- ✅ API返回实际的表格数据而不是91402错误
- ✅ 能够看到表格中的字段和记录

## 📞 如果仍有问题

如果按照上述步骤操作后仍有问题：
1. 截图权限配置页面
2. 提供最新的错误信息
3. 运行诊断脚本并提供输出结果

**你的应用链接**: https://open.feishu.cn/app/cli_a8fe3e73bd78d00d
**权限申请链接**: https://open.feishu.cn/app/cli_a8fe3e73bd78d00d/auth?q=bitable:app,bitable:app:readonly,wiki:wiki:readonly
