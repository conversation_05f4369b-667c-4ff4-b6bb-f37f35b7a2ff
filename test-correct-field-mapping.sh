#!/bin/bash

# 使用正确字段名称测试字段映射功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试数据配置
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  使用正确字段名称测试字段映射${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

echo -e "${YELLOW}1. 启动服务器...${NC}"
nohup java -jar target/ziniao-ai-demo-1.0.0.jar > logs/app.log 2>&1 &
sleep 10

SERVER_URL="http://localhost:8080"

# 检查服务器状态
if curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器启动失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}2. 测试字段映射功能...${NC}"

# 根据实际字段名称构建正确的字段映射
# 从前面的分析可以看到，实际的图片字段是：
# - upperOriginUrl附件 (上装原图附件)
# - downOriginUrl附件 (下装原图附件) 
# - modelImageUrl附件 (模特图片附件)

echo "使用正确的字段映射关系:"
echo "  upperOriginUrl附件 -> upperOriginUrl"
echo "  downOriginUrl附件 -> downOriginUrl"
echo "  modelImageUrl附件 -> modelImageUrl"

RESPONSE=$(curl -s -X POST "$SERVER_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 2,
    "fieldMapping": {
      "upperOriginUrl附件": "upperOriginUrl",
      "downOriginUrl附件": "downOriginUrl",
      "modelImageUrl附件": "modelImageUrl"
    }
  }')

echo ""
echo -e "${YELLOW}3. 响应结果:${NC}"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"

echo ""
echo -e "${YELLOW}4. 分析结果...${NC}"

# 分析响应
ANALYSIS=$(echo "$RESPONSE" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'success' in data and data['success']:
        result_data = data.get('data', {})
        total_records = result_data.get('totalRecords', 0)
        total_images = result_data.get('totalImages', 0)
        successful_downloads = result_data.get('successfulDownloads', 0)
        failed_downloads = result_data.get('failedDownloads', 0)
        
        print(f'✅ 请求成功')
        print(f'📊 统计信息:')
        print(f'   总记录数: {total_records}')
        print(f'   总图片数: {total_images}')
        print(f'   成功下载: {successful_downloads}')
        print(f'   下载失败: {failed_downloads}')
        
        if total_images > 0:
            print(f'🎯 字段映射功能正常工作!')
            
            # 检查记录详情
            records = result_data.get('records', [])
            if records:
                print(f'📝 记录详情:')
                for i, record in enumerate(records):
                    record_id = record.get('recordId', '未知')
                    image_fields = record.get('imageFields', {})
                    print(f'   记录 {i+1} (ID: {record_id}):')
                    for field_name, field_data in image_fields.items():
                        if isinstance(field_data, list) and len(field_data) > 0:
                            for j, img in enumerate(field_data):
                                status = img.get('downloadStatus', '未知')
                                local_url = img.get('localAccessUrl', '无')
                                print(f'     {field_name}[{j}]: {status} -> {local_url}')
        else:
            print(f'❌ 没有找到图片，可能的原因:')
            print(f'   1. 字段名称仍然不正确')
            print(f'   2. 字段映射逻辑有问题')
            print(f'   3. 记录中的字段为空')
    else:
        error_msg = data.get('message', '未知错误')
        print(f'❌ 请求失败: {error_msg}')
        
        # 如果是字段名称错误，显示详细信息
        if 'InvalidFieldNames' in str(data):
            print(f'🔍 这是字段名称错误，需要检查字段映射关系')
except Exception as e:
    print(f'❌ 解析错误: {e}')
" 2>/dev/null)

echo "$ANALYSIS"

echo ""
echo -e "${YELLOW}5. 如果仍然有问题，尝试其他字段组合...${NC}"

# 如果上面的测试失败，尝试其他可能的字段组合
if echo "$RESPONSE" | grep -q '"totalImages": 0'; then
    echo "尝试使用其他字段名称组合..."
    
    # 尝试不带"附件"后缀的字段名
    echo "测试组合1: 不带附件后缀"
    RESPONSE2=$(curl -s -X POST "$SERVER_URL/api/feishu/bitable/images/field-mapping-upload" \
      -H "Content-Type: application/json" \
      -d '{
        "appToken": "'$APP_TOKEN'",
        "tableId": "'$TABLE_ID'",
        "viewId": "'$VIEW_ID'",
        "pageSize": 1,
        "fieldMapping": {
          "upperOriginUrl": "upperOriginUrl_processed",
          "downOriginUrl": "downOriginUrl_processed"
        }
      }')
    
    echo "响应2:"
    echo "$RESPONSE2" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    result_data = data.get('data', {})
    total_images = result_data.get('totalImages', 0)
    print(f'总图片数: {total_images}')
    if total_images > 0:
        print('✅ 找到了正确的字段组合!')
    else:
        print('❌ 仍然没有找到图片')
except:
    print('解析失败')
" 2>/dev/null
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo "测试完成！"

# 停止服务器
pkill -f "java.*ziniao"
