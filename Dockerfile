# 紫鸟AI穿衣演示 - 离线Docker镜像
# 创建一个可以完全离线运行的自包含Docker镜像

# 单阶段构建：使用Maven镜像（包含JDK和JRE）
FROM maven:3.8-openjdk-8

# 安装必要工具
RUN apt-get update && apt-get install -y curl wget && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制所有项目文件
COPY . .

# 确保lib目录存在并下载依赖（如果需要）
RUN mkdir -p lib && \
    if [ ! "$(ls -A lib 2>/dev/null)" ]; then \
        echo "正在下载离线运行所需的依赖..." && \
        curl -L -o lib/okhttp-4.9.3.jar https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp/4.9.3/okhttp-4.9.3.jar && \
        curl -L -o lib/okhttp-sse-4.9.3.jar https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp-sse/4.9.3/okhttp-sse-4.9.3.jar && \
        curl -L -o lib/logging-interceptor-4.9.3.jar https://repo1.maven.org/maven2/com/squareup/okhttp3/logging-interceptor/4.9.3/logging-interceptor-4.9.3.jar && \
        curl -L -o lib/okio-2.8.0.jar https://repo1.maven.org/maven2/com/squareup/okio/okio/2.8.0/okio-2.8.0.jar && \
        curl -L -o lib/kotlin-stdlib-1.4.10.jar https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib/1.4.10/kotlin-stdlib-1.4.10.jar && \
        curl -L -o lib/kotlin-stdlib-common-1.4.10.jar https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib-common/1.4.10/kotlin-stdlib-common-1.4.10.jar && \
        curl -L -o lib/annotations-13.0.jar https://repo1.maven.org/maven2/org/jetbrains/annotations/13.0/annotations-13.0.jar; \
    fi

# 下载Maven依赖（用于离线构建）
RUN mvn dependency:go-offline -B || true

# 构建应用程序
RUN mvn clean package -DskipTests -B

# 创建非root用户
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -s /bin/bash -m appuser

# 创建日志目录
RUN mkdir -p logs && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || curl -f http://localhost:8080/ || exit 1

# 设置JVM选项
ENV JAVA_OPTS="-Xmx512m -Xms256m -Djava.security.egd=file:/dev/./urandom"

# 启动应用
CMD ["sh", "-c", "java $JAVA_OPTS -jar target/ziniao-ai-demo-1.0.0.jar"]
