#!/bin/bash

# AI穿戴接口测试脚本

# 设置服务器地址
SERVER_URL="http://localhost:8080"

# 测试AI穿戴接口
echo "=== 测试AI穿戴接口 ==="

# 构建请求数据（使用官方文档中的示例数据）
REQUEST_DATA='{
  "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/06/f8703b6b802c4c6c83fad152e2a7c07d.webp",
  "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/06/10dbbb7649a5445c87184ab287ed3b4b.png",
  "targetMaskUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/16/3ce9190e4ce64f2f88406d36409ffc55.png",
  "outputNum": 1
}'

echo "请求数据:"
echo "$REQUEST_DATA"

echo ""
echo "发送AI穿戴请求..."

# 发送请求
RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/intelligentWear" \
  -H "Content-Type: application/json" \
  -d "$REQUEST_DATA")

echo "响应结果:"
echo "$RESPONSE"

# 简单检查响应
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo ""
    echo "✅ AI穿戴请求提交成功!"
    
    # 尝试提取任务ID（简单方式）
    if echo "$RESPONSE" | grep -q '"id"'; then
        echo "响应中包含任务ID"
        
        echo ""
        echo "=== 测试健康检查接口 ==="
        HEALTH_RESPONSE=$(curl -s -X GET "$SERVER_URL/api/clothing/health")
        echo "健康检查响应: $HEALTH_RESPONSE"
    else
        echo "⚠️  响应中未找到任务ID"
    fi
else
    echo ""
    echo "❌ AI穿戴请求失败"
    echo "请检查服务是否正常运行和配置是否正确"
fi

echo ""
echo "=== 测试完成 ==="
