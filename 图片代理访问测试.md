# 图片代理访问功能测试

## 🧪 测试计划

### 1. 配置测试
- ✅ 添加图片代理配置到 `application-prod.yml`
- ✅ 创建 `ImageProxyController` 控制器
- ✅ 创建 `ImageProxyService` 服务
- ✅ 修改 `FileUploadService` 支持代理URL
- ✅ 修改 `FeishuBitableService` 支持代理URL

### 2. 功能测试步骤

#### 步骤1：启动应用并验证配置
```bash
# 启动应用
java -jar ziniao-ai-demo.jar

# 检查日志中是否有图片代理相关的配置信息
tail -f logs/ziniao-ai-demo.log | grep -i "proxy\|image"
```

#### 步骤2：测试文件上传（代理URL）
```bash
# 上传图片文件
curl -X POST \
  -F "file=@test-image.png" \
  "http://39.108.93.224:18088/api/file/upload"

# 期望响应包含代理URL：
# {
#   "success": true,
#   "data": {
#     "fileUrl": "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6"
#   }
# }
```

#### 步骤3：测试代理URL访问
```bash
# 使用返回的代理URL访问图片
curl "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6" \
  -o downloaded-image.png

# 验证下载的图片是否正确
file downloaded-image.png
```

#### 步骤4：测试飞书图片下载（代理URL）
```bash
# 获取飞书图片
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "imageFields": ["image_field"]
  }' \
  "http://39.108.93.224:18088/api/feishu/bitable/images"

# 期望响应中的图片URL为代理格式
```

#### 步骤5：测试图片信息获取
```bash
# 获取图片信息
curl "http://39.108.93.224:18088/api/image-proxy/info/encoded_path"

# 期望响应：
# {
#   "filename": "image.png",
#   "size": 1024000,
#   "contentType": "image/png",
#   "lastModified": 1732523456789
# }
```

#### 步骤6：测试URL生成接口
```bash
# 生成代理URL
curl -X POST \
  -d "filePath=/uploads/2025/07/25/test.png" \
  "http://39.108.93.224:18088/api/image-proxy/generate-url"

# 期望响应：
# {
#   "originalPath": "/uploads/2025/07/25/test.png",
#   "proxyUrl": "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6"
# }
```

### 3. 兼容性测试

#### 测试禁用代理访问
```yaml
# 修改配置
image:
  proxy:
    enabled: false
```

```bash
# 重启应用后测试上传
curl -X POST \
  -F "file=@test-image.png" \
  "http://39.108.93.224:18088/api/file/upload"

# 期望返回传统的直接URL：
# {
#   "success": true,
#   "data": {
#     "fileUrl": "http://39.108.93.224:18088/uploads/2025/07/25/image.png"
#   }
# }
```

### 4. 错误处理测试

#### 测试无效的图片ID
```bash
curl "http://39.108.93.224:18088/api/image-proxy/id/invalid_id"
# 期望：404 Not Found
```

#### 测试无效的加密路径
```bash
curl "http://39.108.93.224:18088/api/image-proxy/view/invalid_encoded_path"
# 期望：404 Not Found
```

### 5. 性能测试

#### 并发访问测试
```bash
# 使用ab工具进行并发测试
ab -n 100 -c 10 "http://39.108.93.224:18088/api/image-proxy/id/valid_id"
```

#### 缓存效果测试
```bash
# 多次访问同一图片，检查响应时间
for i in {1..10}; do
  time curl -s "http://39.108.93.224:18088/api/image-proxy/id/valid_id" > /dev/null
done
```

## 📋 测试检查清单

### 基础功能
- [ ] 应用正常启动，无错误日志
- [ ] 图片代理配置正确加载
- [ ] ImageProxyController 正常注册
- [ ] ImageProxyService 正常初始化

### 上传功能
- [ ] 文件上传返回代理URL（启用时）
- [ ] 文件上传返回直接URL（禁用时）
- [ ] 图片文件正确识别
- [ ] 非图片文件使用直接URL

### 访问功能
- [ ] 通过图片ID正常访问
- [ ] 通过加密路径正常访问
- [ ] 正确的Content-Type响应头
- [ ] 正确的文件内容

### 飞书集成
- [ ] 飞书图片下载返回代理URL
- [ ] ID映射正确注册
- [ ] 下载的图片可通过代理访问

### 错误处理
- [ ] 无效ID返回404
- [ ] 无效路径返回404
- [ ] 文件不存在返回404
- [ ] 路径遍历攻击被阻止

### 性能表现
- [ ] 首次访问响应时间合理
- [ ] 缓存命中响应时间更快
- [ ] 并发访问稳定
- [ ] 内存使用正常

## 🐛 常见问题排查

### 1. 代理URL不生效
**检查项：**
- `image.proxy.enabled` 是否为 `true`
- `ImageProxyService` 是否正常注入
- 日志中是否有相关错误信息

### 2. 图片无法访问
**检查项：**
- 图片文件是否存在
- 文件权限是否正确
- ID映射是否正确建立

### 3. 性能问题
**检查项：**
- 缓存配置是否合理
- 并发访问是否过高
- 磁盘IO是否成为瓶颈

## 📊 测试报告模板

```
测试日期：____
测试环境：____
测试人员：____

基础功能测试：
- 应用启动：✅/❌
- 配置加载：✅/❌
- 服务注册：✅/❌

上传功能测试：
- 代理URL生成：✅/❌
- 直接URL回退：✅/❌
- 文件类型识别：✅/❌

访问功能测试：
- ID访问：✅/❌
- 路径访问：✅/❌
- 内容正确性：✅/❌

集成功能测试：
- 飞书集成：✅/❌
- ID映射：✅/❌

错误处理测试：
- 404处理：✅/❌
- 安全检查：✅/❌

性能测试：
- 响应时间：____ms
- 并发处理：____req/s
- 内存使用：____MB

问题记录：
1. ____
2. ____

建议：
1. ____
2. ____
```

## 🎯 验收标准

### 功能完整性
- ✅ 所有API接口正常工作
- ✅ 配置开关生效
- ✅ 错误处理完善

### 性能要求
- ✅ 单次访问响应时间 < 500ms
- ✅ 并发100请求成功率 > 99%
- ✅ 内存使用增长 < 50MB

### 安全性
- ✅ 路径遍历攻击防护
- ✅ 文件类型验证
- ✅ 访问权限控制

### 兼容性
- ✅ 向后兼容现有URL
- ✅ 配置热切换
- ✅ 优雅降级
