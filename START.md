# 启动指南

## 环境要求

- Java 8 或更高版本
- Maven 3.6 或更高版本

## 快速启动

### 1. 安装Maven（如果未安装）

**macOS:**
```bash
brew install maven
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install maven
```

**Windows:**
下载并安装 Maven：https://maven.apache.org/download.cgi

### 2. 配置API密钥

编辑 `src/main/resources/application.yml`：

```yaml
ziniao:
  api:
    app-id: your_app_id_here          # 替换为您的应用ID
    private-key: your_private_key_here # 替换为您的应用私钥
```

### 3. 启动应用

```bash
# 方式1: 使用Maven直接启动
mvn spring-boot:run

# 方式2: 先打包再启动
mvn clean package -DskipTests
java -jar target/ziniao-ai-demo-1.0.0.jar

# 方式3: 使用构建脚本
./build.sh
```

### 4. 访问应用

启动成功后，访问以下地址：

- **API文档 (Knife4j)**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui/
- **应用首页**: http://localhost:8080

## API使用示例

### 1. 获取应用令牌

```bash
curl -X GET "http://localhost:8080/api/token/app"
```

### 2. AI穿衣处理

```bash
curl -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://example.com/person.jpg",
    "clothingImage": "https://example.com/clothing.jpg",
    "clothingType": "上衣",
    "style": "casual",
    "quality": "high"
  }'
```

### 3. 查询任务状态

```bash
curl -X GET "http://localhost:8080/api/clothing/task/{taskId}"
```

## 故障排除

### 1. 端口冲突

如果8080端口被占用，可以修改 `application.yml` 中的端口：

```yaml
server:
  port: 8081
```

### 2. API密钥错误

确保在 `application.yml` 中配置了正确的应用ID和私钥。

### 3. 网络连接问题

确保能够访问紫鸟API服务器：`https://sbappstoreapi.ziniao.com`

### 4. 依赖下载失败

如果Maven依赖下载失败，可以尝试：

```bash
mvn clean install -U
```

## 开发说明

### 项目结构

- `controller/` - REST API控制器
- `service/` - 业务逻辑服务
- `model/` - 数据模型
- `config/` - 配置类
- `demo/` - 演示代码

### 添加新功能

1. 在 `service/` 中添加业务逻辑
2. 在 `controller/` 中添加REST接口
3. 在 `model/` 中定义数据模型
4. 更新Swagger注解以生成文档

### 测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=ZiniaoApiServiceTest
```

## 联系支持

如有问题，请参考：
- 紫鸟开放平台文档
- 项目README.md
- 或提交Issue
