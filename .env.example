# Docker Compose环境变量配置
# 复制此文件为.env并填入您的实际值

# 构建信息（可选）
BUILD_DATE=
VCS_REF=

# 紫鸟API配置
# 请将这些替换为您的实际API凭据
ZINIAO_API_APP_ID=your_app_id_here
ZINIAO_API_PRIVATE_KEY=your_private_key_here
ZINIAO_API_BASE_URL=https://sbappstoreapi.ziniao.com

# 应用配置
SERVER_PORT=8080
SPRING_PROFILES_ACTIVE=docker

# 服务器基础URL配置（重要：部署时必须修改）
# 本地开发: http://localhost:8080
# 服务器部署: http://你的服务器IP:端口
# 例如: http://*************:18088
SERVER_BASE_URL=http://localhost:8080

# 日志配置
LOGGING_LEVEL_COM_ZINIAO=INFO
LOGGING_LEVEL_ROOT=WARN

# JVM配置
JAVA_OPTS=-Xmx512m -Xms256m -Djava.security.egd=file:/dev/./urandom
