# AI穿戴接口使用指南

## 接口概述

AI穿戴接口用于将商品穿戴到模特身上，实现智能穿戴效果。

## 接口信息

- **接口路径**: `/api/clothing/intelligentWear`
- **请求方式**: `POST`
- **Content-Type**: `application/json`

## 请求参数

| 参数名称 | 参数说明 | 是否必须 | 数据类型 | 示例值 |
|---------|---------|---------|---------|--------|
| imageUrl | 穿戴商品图 | 是 | string | https://example.com/product.jpg |
| targetOriginUrl | 穿戴模特原图 1. 图片地址 2. 调用作图素材取地址(type=7) | 是 | string | https://example.com/model.jpg |
| targetMaskUrl | 穿戴模特穿戴区域图片 targetOriginUrl为自定义图片地址时 必填（黑白图尺寸与原图一致） | 否 | string | https://example.com/mask.jpg |
| outputNum | 输出张数 取值范围：[1,4] 默认值：1 | 否 | integer | 1 |

## 请求示例

```bash
curl -X POST "http://localhost:8080/api/clothing/intelligentWear" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/06/f8703b6b802c4c6c83fad152e2a7c07d.webp",
    "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/06/10dbbb7649a5445c87184ab287ed3b4b.png",
    "targetMaskUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1795336734728318976/2024/12/16/3ce9190e4ce64f2f88406d36409ffc55.png",
    "outputNum": 1
  }'
```

## 响应格式

### 成功响应

```json
{
  "request_id": "xxx",
  "code": "0",
  "msg": "成功",
  "sub_code": null,
  "sub_msg": null,
  "data": {
    "msg": null,
    "code": 200,
    "traceId": "xxx",
    "data": {
      "id": "1945877291232894976"
    },
    "msgKey": null
  },
  "success": true,
  "task_id": null
}
```

### 错误响应

```json
{
  "code": "400",
  "msg": "参数错误",
  "success": false
}
```

## 使用流程

1. **提交任务**: 调用AI穿戴接口，提交处理任务
2. **获取任务ID**: 从响应中获取任务ID (`data.data.id`)
3. **查询结果**: 使用任务ID查询处理结果

## 查询任务结果

使用返回的任务ID查询处理结果：

```bash
curl -X GET "http://localhost:8080/api/clothing/result/{taskId}"
```

## 测试脚本

项目中提供了测试脚本 `test-intelligent-wear.sh`，可以直接运行测试：

```bash
./test-intelligent-wear.sh
```

## 注意事项

1. **图片格式**: 支持常见的图片格式（JPG、PNG、WebP等）
2. **图片大小**: 建议图片大小不超过10MB
3. **处理时间**: AI穿戴处理需要一定时间，请耐心等待
4. **蒙版图片**: 当使用自定义模特图片时，需要提供对应的蒙版图片
5. **输出数量**: 可以指定输出1-4张图片，默认为1张

## 参数详解

### imageUrl（穿戴商品图）
- 要穿戴的商品图片URL
- 建议使用清晰、背景简洁的商品图

### targetOriginUrl（穿戴模特原图）
- 模特原图URL
- 可以是自定义图片地址
- 也可以调用作图素材取地址(type=7)

### targetMaskUrl（穿戴区域图片）
- 当targetOriginUrl为自定义图片地址时必填
- 黑白图，尺寸与原图一致
- 白色区域表示穿戴区域，黑色区域表示保留区域

### outputNum（输出张数）
- 取值范围：[1,4]
- 默认值：1
- 指定生成图片的数量

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |
| 20001 | 授权权限不足 |

## 技术支持

如有问题，请查看日志文件或联系技术支持。
