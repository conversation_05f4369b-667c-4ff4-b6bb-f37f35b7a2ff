#!/bin/bash

# 简单的Java编译脚本（不依赖Maven）

echo "=== 紫鸟AI穿衣API Java Demo 编译脚本 ==="

# 检查Java是否安装
if ! command -v javac &> /dev/null; then
    echo "错误: Java编译器(javac)未安装，请先安装JDK"
    exit 1
fi

echo "Java版本:"
java -version
echo ""

# 创建输出目录
mkdir -p target/classes
mkdir -p target/test-classes
mkdir -p logs

echo "=== 下载依赖JAR文件 ==="

# 创建lib目录
mkdir -p lib

# 下载依赖（如果不存在）
DEPS=(
    "https://repo1.maven.org/maven2/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar"
    "https://repo1.maven.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar"
    "https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"
    "https://repo1.maven.org/maven2/commons-codec/commons-codec/1.11/commons-codec-1.11.jar"
    "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar"
    "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar"
    "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.0/jackson-annotations-2.15.0.jar"
    "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar"
    "https://repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar"
    "https://repo1.maven.org/maven2/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar"
)

for dep in "${DEPS[@]}"; do
    filename=$(basename "$dep")
    if [ ! -f "lib/$filename" ]; then
        echo "下载 $filename..."
        if command -v curl &> /dev/null; then
            curl -L -o "lib/$filename" "$dep"
        elif command -v wget &> /dev/null; then
            wget -O "lib/$filename" "$dep"
        else
            echo "错误: 需要curl或wget来下载依赖"
            echo "请手动下载以下文件到lib目录:"
            for d in "${DEPS[@]}"; do
                echo "  $d"
            done
            exit 1
        fi
    else
        echo "$filename 已存在，跳过下载"
    fi
done

echo ""
echo "=== 编译Java源文件 ==="

# 构建classpath
CLASSPATH="lib/*"

# 编译主要源文件
echo "编译主要源文件..."
find src/main/java -name "*.java" -print0 | xargs -0 javac -cp "$CLASSPATH" -d target/classes

if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

# 复制资源文件
if [ -d "src/main/resources" ]; then
    echo "复制资源文件..."
    cp -r src/main/resources/* target/classes/
fi

echo "编译成功！"
echo ""
echo "运行Demo:"
echo "java -cp \"target/classes:lib/*\" com.ziniao.demo.ClothingDemo"
echo ""
echo "注意: 请先在 src/main/java/com/ziniao/config/ApiConfig.java 中配置您的API密钥"
