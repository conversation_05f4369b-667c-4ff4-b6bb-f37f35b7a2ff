#!/bin/bash

# 字段映射图片上传功能测试脚本
# 测试新的 /api/feishu/bitable/images/field-mapping-upload 接口

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_URL="http://localhost:8080"
ENDPOINT="/api/feishu/bitable/images/field-mapping-upload"

# 测试数据配置
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  字段映射图片上传功能测试${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查服务器状态
echo -e "${YELLOW}1. 检查服务器状态...${NC}"
if curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器未启动或无法访问${NC}"
    echo "请确保服务器在 $SERVER_URL 上运行"
    exit 1
fi

echo ""

# 测试1: 基本字段映射功能
echo -e "${YELLOW}2. 测试基本字段映射功能...${NC}"
echo "接口: POST $SERVER_URL$ENDPOINT"
echo ""

RESPONSE=$(curl -s -X POST "$SERVER_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "pageSize": 2,
    "updateBitableWithLocalUrl": true,
    "includeImageDetails": true
  }')

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 检查响应状态
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 基本字段映射测试成功${NC}"
    
    # 提取统计信息
    TOTAL_IMAGES=$(echo "$RESPONSE" | grep -o '"totalImages":[0-9]*' | cut -d':' -f2)
    SUCCESS_COUNT=$(echo "$RESPONSE" | grep -o '"successfulDownloads":[0-9]*' | cut -d':' -f2)
    FAILED_COUNT=$(echo "$RESPONSE" | grep -o '"failedDownloads":[0-9]*' | cut -d':' -f2)
    
    echo "📊 处理统计:"
    echo "   - 总图片数: $TOTAL_IMAGES"
    echo "   - 成功下载: $SUCCESS_COUNT"
    echo "   - 下载失败: $FAILED_COUNT"
else
    echo -e "${RED}❌ 基本字段映射测试失败${NC}"
fi

echo ""
echo -e "${BLUE}----------------------------------------${NC}"

# 测试2: 多字段映射功能
echo -e "${YELLOW}3. 测试多字段映射功能...${NC}"

RESPONSE2=$(curl -s -X POST "$SERVER_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url",
      "👖下装正面图": "👖下装正面图url",
      "👖下装背面图": "👖下装背面图url"
    },
    "pageSize": 3,
    "updateBitableWithLocalUrl": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 2
  }')

echo "响应结果:"
echo "$RESPONSE2" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE2"
echo ""

if echo "$RESPONSE2" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 多字段映射测试成功${NC}"
else
    echo -e "${RED}❌ 多字段映射测试失败${NC}"
fi

echo ""
echo -e "${BLUE}----------------------------------------${NC}"

# 测试3: 指定记录ID测试
echo -e "${YELLOW}4. 测试指定记录ID功能...${NC}"

RESPONSE3=$(curl -s -X POST "$SERVER_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "recordId": "recqwIwhc6",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "updateBitableWithLocalUrl": true
  }')

echo "响应结果:"
echo "$RESPONSE3" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE3"
echo ""

if echo "$RESPONSE3" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 指定记录ID测试成功${NC}"
else
    echo -e "${RED}❌ 指定记录ID测试失败${NC}"
fi

echo ""
echo -e "${BLUE}----------------------------------------${NC}"

# 测试4: 禁用写回功能测试
echo -e "${YELLOW}5. 测试禁用写回功能...${NC}"

RESPONSE4=$(curl -s -X POST "$SERVER_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "pageSize": 1,
    "updateBitableWithLocalUrl": false
  }')

echo "响应结果:"
echo "$RESPONSE4" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE4"
echo ""

if echo "$RESPONSE4" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 禁用写回功能测试成功${NC}"
    echo "   (图片已下载但未写回多维表格)"
else
    echo -e "${RED}❌ 禁用写回功能测试失败${NC}"
fi

echo ""
echo -e "${BLUE}========================================${NC}"

# 总结
echo -e "${BLUE}📋 测试总结${NC}"
echo ""
echo "✨ 功能特点:"
echo "1. 支持自定义字段映射关系"
echo "2. 支持多个字段同时映射处理"
echo "3. 支持指定记录ID处理"
echo "4. 支持启用/禁用写回功能"
echo "5. 支持并发下载控制"
echo ""
echo "🔧 使用场景:"
echo "- 服装图片管理：👚上装正面图 -> 👚上装正面图url"
echo "- 产品图片处理：产品主图 -> 产品主图url"
echo "- 批量图片本地化：任意图片字段 -> 对应URL字段"
echo ""
echo "📖 详细文档: FIELD_MAPPING_UPLOAD_GUIDE.md"
echo ""
echo "测试完成！"
