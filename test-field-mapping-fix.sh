#!/bin/bash

# 字段映射图片上传功能修复测试脚本
# 测试修复后的 /api/feishu/bitable/images/field-mapping-upload 接口

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_URL="http://localhost:8080"
ENDPOINT="/api/feishu/bitable/images/field-mapping-upload"

# 测试数据配置
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  字段映射图片上传功能修复测试${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查服务器状态
echo -e "${YELLOW}1. 检查服务器状态...${NC}"
if curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器未启动或无法访问${NC}"
    echo "请确保服务器在 $SERVER_URL 上运行"
    exit 1
fi

echo ""

# 测试修复后的字段映射功能
echo -e "${YELLOW}2. 测试修复后的字段映射功能...${NC}"
echo "接口: POST $SERVER_URL$ENDPOINT"
echo ""

RESPONSE=$(curl -s -X POST "$SERVER_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "pageSize": 2,
    "updateBitableWithLocalUrl": true,
    "includeImageDetails": true
  }')

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 检查响应状态
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 字段映射修复测试成功${NC}"
    
    # 提取统计信息
    TOTAL_IMAGES=$(echo "$RESPONSE" | grep -o '"totalImages":[0-9]*' | cut -d':' -f2)
    SUCCESS_COUNT=$(echo "$RESPONSE" | grep -o '"successfulDownloads":[0-9]*' | cut -d':' -f2)
    FAILED_COUNT=$(echo "$RESPONSE" | grep -o '"failedDownloads":[0-9]*' | cut -d':' -f2)
    
    echo "📊 处理统计:"
    echo "   - 总图片数: $TOTAL_IMAGES"
    echo "   - 成功下载: $SUCCESS_COUNT"
    echo "   - 下载失败: $FAILED_COUNT"
elif echo "$RESPONSE" | grep -q '"code":1254024'; then
    echo -e "${RED}❌ 仍然存在 InvalidFieldNames 错误${NC}"
    echo "需要进一步检查字段名称处理逻辑"
elif echo "$RESPONSE" | grep -q '"success":false'; then
    echo -e "${YELLOW}⚠️ 请求失败，但不是字段名称错误${NC}"
    ERROR_MSG=$(echo "$RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    echo "错误信息: $ERROR_MSG"
else
    echo -e "${RED}❌ 未知错误${NC}"
fi

echo ""
echo -e "${BLUE}----------------------------------------${NC}"

# 测试多字段映射
echo -e "${YELLOW}3. 测试多字段映射功能...${NC}"

RESPONSE2=$(curl -s -X POST "$SERVER_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "pageSize": 1,
    "updateBitableWithLocalUrl": true
  }')

echo "响应结果:"
echo "$RESPONSE2" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE2"
echo ""

if echo "$RESPONSE2" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 多字段映射测试成功${NC}"
elif echo "$RESPONSE2" | grep -q '"code":1254024'; then
    echo -e "${RED}❌ 多字段映射仍然存在 InvalidFieldNames 错误${NC}"
else
    echo -e "${YELLOW}⚠️ 多字段映射测试失败${NC}"
fi

echo ""
echo -e "${BLUE}========================================${NC}"

# 总结
echo -e "${BLUE}📋 修复测试总结${NC}"
echo ""
echo "🔧 修复内容:"
echo "1. 移除了字段映射请求中的 field_names 参数"
echo "2. 改为获取所有字段，在处理时进行过滤"
echo "3. 避免了字段名称URL编码导致的 InvalidFieldNames 错误"
echo ""
echo "✨ 如果测试成功，说明修复有效"
echo "❌ 如果仍有错误，需要进一步调试"
echo ""
echo "测试完成！"
