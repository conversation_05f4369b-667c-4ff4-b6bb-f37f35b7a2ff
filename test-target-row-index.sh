#!/bin/bash

# 测试targetRowIndex功能的脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试数据配置
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  测试targetRowIndex功能${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

echo -e "${YELLOW}1. 启动服务器...${NC}"
nohup java -jar target/ziniao-ai-demo-1.0.0.jar > logs/app.log 2>&1 &
sleep 10

SERVER_URL="http://localhost:8080"

# 检查服务器状态
if curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器启动失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}2. 测试1: 不指定targetRowIndex（处理所有行）${NC}"

RESPONSE1=$(curl -s -X POST "$SERVER_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 5,
    "fieldMapping": {
      "upperOriginUrl附件": "upperOriginUrl",
      "downOriginUrl附件": "downOriginUrl",
      "modelImageUrl附件": "modelImageUrl"
    }
  }')

echo "响应1 (所有行):"
ANALYSIS1=$(echo "$RESPONSE1" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'success' in data and data['success']:
        result_data = data.get('data', {})
        total_records = result_data.get('totalRecords', 0)
        total_images = result_data.get('totalImages', 0)
        print(f'✅ 成功处理 {total_records} 条记录，{total_images} 张图片')
        
        records = result_data.get('records', [])
        for i, record in enumerate(records):
            record_id = record.get('recordId', '未知')
            print(f'  记录 {i+1}: {record_id}')
    else:
        print(f'❌ 请求失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析错误: {e}')
" 2>/dev/null)

echo "$ANALYSIS1"

echo ""
echo -e "${YELLOW}3. 测试2: 指定targetRowIndex=1（只处理第1行）${NC}"

RESPONSE2=$(curl -s -X POST "$SERVER_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 5,
    "targetRowIndex": 1,
    "fieldMapping": {
      "upperOriginUrl附件": "upperOriginUrl",
      "downOriginUrl附件": "downOriginUrl",
      "modelImageUrl附件": "modelImageUrl"
    }
  }')

echo "响应2 (第1行):"
ANALYSIS2=$(echo "$RESPONSE2" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'success' in data and data['success']:
        result_data = data.get('data', {})
        total_records = result_data.get('totalRecords', 0)
        total_images = result_data.get('totalImages', 0)
        print(f'✅ 成功处理 {total_records} 条记录，{total_images} 张图片')
        
        records = result_data.get('records', [])
        for i, record in enumerate(records):
            record_id = record.get('recordId', '未知')
            print(f'  记录 {i+1}: {record_id}')
    else:
        print(f'❌ 请求失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析错误: {e}')
" 2>/dev/null)

echo "$ANALYSIS2"

echo ""
echo -e "${YELLOW}4. 测试3: 指定targetRowIndex=2（只处理第2行）${NC}"

RESPONSE3=$(curl -s -X POST "$SERVER_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 5,
    "targetRowIndex": 2,
    "fieldMapping": {
      "upperOriginUrl附件": "upperOriginUrl",
      "downOriginUrl附件": "downOriginUrl",
      "modelImageUrl附件": "modelImageUrl"
    }
  }')

echo "响应3 (第2行):"
ANALYSIS3=$(echo "$RESPONSE3" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'success' in data and data['success']:
        result_data = data.get('data', {})
        total_records = result_data.get('totalRecords', 0)
        total_images = result_data.get('totalImages', 0)
        print(f'✅ 成功处理 {total_records} 条记录，{total_images} 张图片')
        
        records = result_data.get('records', [])
        for i, record in enumerate(records):
            record_id = record.get('recordId', '未知')
            print(f'  记录 {i+1}: {record_id}')
    else:
        print(f'❌ 请求失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析错误: {e}')
" 2>/dev/null)

echo "$ANALYSIS3"

echo ""
echo -e "${YELLOW}5. 测试4: 指定targetRowIndex=999（超出范围）${NC}"

RESPONSE4=$(curl -s -X POST "$SERVER_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 5,
    "targetRowIndex": 999,
    "fieldMapping": {
      "upperOriginUrl附件": "upperOriginUrl",
      "downOriginUrl附件": "downOriginUrl",
      "modelImageUrl附件": "modelImageUrl"
    }
  }')

echo "响应4 (超出范围):"
ANALYSIS4=$(echo "$RESPONSE4" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if 'success' in data and data['success']:
        result_data = data.get('data', {})
        total_records = result_data.get('totalRecords', 0)
        total_images = result_data.get('totalImages', 0)
        print(f'✅ 成功处理 {total_records} 条记录，{total_images} 张图片')
        
        if total_records == 0:
            print('  ✅ 正确处理了超出范围的情况，没有处理任何记录')
    else:
        print(f'❌ 请求失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析错误: {e}')
" 2>/dev/null)

echo "$ANALYSIS4"

echo ""
echo -e "${YELLOW}6. 功能验证总结${NC}"

echo "✅ targetRowIndex功能测试完成！"
echo ""
echo "功能说明:"
echo "  - targetRowIndex不填或为null：处理所有行"
echo "  - targetRowIndex=1：只处理第1行数据"
echo "  - targetRowIndex=2：只处理第2行数据"
echo "  - targetRowIndex超出范围：不处理任何数据，返回空结果"
echo ""
echo "使用示例:"
echo '  {
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "targetRowIndex": 1,
    "fieldMapping": {
      "upperOriginUrl附件": "upperOriginUrl"
    }
  }'

echo ""
echo -e "${BLUE}========================================${NC}"
echo "测试完成！"

# 停止服务器
pkill -f "java.*ziniao"
