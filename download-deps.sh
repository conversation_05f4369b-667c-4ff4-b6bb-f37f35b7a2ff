#!/bin/bash

# 快速下载必要依赖

echo "=== 下载紫鸟SDK所需依赖 ==="

mkdir -p lib

# 必要的依赖列表
DEPS=(
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp/4.9.3/okhttp-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp-sse/4.9.3/okhttp-sse-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/logging-interceptor/4.9.3/logging-interceptor-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okio/okio/2.8.0/okio-2.8.0.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib/1.4.10/kotlin-stdlib-1.4.10.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib-common/1.4.10/kotlin-stdlib-common-1.4.10.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/annotations/13.0/annotations-13.0.jar"
)

for dep in "${DEPS[@]}"; do
    filename=$(basename "$dep")
    if [ ! -f "lib/$filename" ]; then
        echo "下载 $filename..."
        curl -L --connect-timeout 10 --max-time 30 -o "lib/$filename" "$dep"
        if [ $? -eq 0 ]; then
            echo "✅ $filename 下载成功"
        else
            echo "❌ $filename 下载失败"
        fi
    else
        echo "✅ $filename 已存在"
    fi
done

echo ""
echo "=== 依赖下载完成 ==="
echo ""
echo "现在重新启动应用："
echo "1. 停止当前应用 (Ctrl+C)"
echo "2. 重新启动应用"
echo ""
echo "或者如果有Maven，直接运行："
echo "mvn spring-boot:run"
