#!/bin/bash

# 按记录ID字段映射图片处理接口测试脚本
# 测试新的 /api/feishu/bitable/images/field-mapping-by-record 接口

# 配置
BASE_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/images/field-mapping-by-record"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_api() {
    local test_name="$1"
    local request_data="$2"
    local expected_status="$3"
    
    log_info "测试: $test_name"
    echo "请求数据: $request_data"
    
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$request_data" \
        "$BASE_URL$API_ENDPOINT")
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $response_body"
    
    if [ "$http_code" = "$expected_status" ]; then
        log_success "测试通过"
    else
        log_error "测试失败，期望状态码: $expected_status，实际状态码: $http_code"
    fi
    
    echo "----------------------------------------"
}

echo "=========================================="
echo "按记录ID字段映射图片处理接口测试"
echo "=========================================="

# 测试1: 精确记录ID处理（摘要模式）
log_info "开始测试1: 精确记录ID处理（摘要模式）"
test_api "精确记录ID处理（摘要模式）" '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
        "👚上装正面图": "👚上装正面图url",
        "👚上装背面图": "👚上装背面图url"
    },
    "recordIds": ["recqwIwhc6", "recABC123"],
    "summaryOnly": true,
    "processId": "test_summary_001"
}' "200"

# 测试2: 精确记录ID处理（详细模式）
log_info "开始测试2: 精确记录ID处理（详细模式）"
test_api "精确记录ID处理（详细模式）" '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
        "👚上装正面图": "👚上装正面图url"
    },
    "recordIds": ["recqwIwhc6"],
    "summaryOnly": false,
    "processId": "test_detail_001"
}' "200"

# 测试3: 智能分页处理
log_info "开始测试3: 智能分页处理"
test_api "智能分页处理" '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
        "👚上装正面图": "👚上装正面图url",
        "👚上装背面图": "👚上装背面图url"
    },
    "enableSmartPaging": true,
    "pageSize": 200,
    "maxPages": 3,
    "summaryOnly": true,
    "processId": "test_paging_001"
}' "200"

# 测试4: 标准处理模式
log_info "开始测试4: 标准处理模式"
test_api "标准处理模式" '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
        "👚上装正面图": "👚上装正面图url"
    },
    "pageSize": 100,
    "summaryOnly": true,
    "processId": "test_standard_001"
}' "200"

# 测试5: 批量处理优化
log_info "开始测试5: 批量处理优化"
test_api "批量处理优化" '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
        "👚上装正面图": "👚上装正面图url",
        "👚上装背面图": "👚上装背面图url"
    },
    "recordIds": ["rec001", "rec002", "rec003", "rec004", "rec005"],
    "batchSize": 2,
    "continueOnError": true,
    "summaryOnly": true,
    "processId": "test_batch_001"
}' "200"

# 测试6: 参数验证（缺少必需参数）
log_info "开始测试6: 参数验证（缺少必需参数）"
test_api "参数验证（缺少必需参数）" '{
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
        "👚上装正面图": "👚上装正面图url"
    }
}' "400"

# 测试7: 参数验证（空字段映射）
log_info "开始测试7: 参数验证（空字段映射）"
test_api "参数验证（空字段映射）" '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {}
}' "400"

# 测试8: 性能测试（跳过已有URL）
log_info "开始测试8: 性能测试（跳过已有URL）"
test_api "性能测试（跳过已有URL）" '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
        "👚上装正面图": "👚上装正面图url"
    },
    "skipExistingUrls": true,
    "summaryOnly": true,
    "maxConcurrentDownloads": 5,
    "processId": "test_performance_001"
}' "200"

echo "=========================================="
log_info "所有测试完成"
echo "=========================================="

# 性能对比测试
echo ""
log_info "开始性能对比测试..."

# 测试原接口
log_info "测试原接口性能..."
start_time=$(date +%s%N)
curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
        "tableId": "tbl4sH8PYHUk36K0",
        "fieldMapping": {
            "👚上装正面图": "👚上装正面图url"
        },
        "pageSize": 100
    }' \
    "$BASE_URL/api/feishu/bitable/images/field-mapping-upload" > /dev/null
end_time=$(date +%s%N)
old_api_time=$((($end_time - $start_time) / 1000000))
log_info "原接口耗时: ${old_api_time}ms"

# 测试新接口（摘要模式）
log_info "测试新接口性能（摘要模式）..."
start_time=$(date +%s%N)
curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
        "tableId": "tbl4sH8PYHUk36K0",
        "fieldMapping": {
            "👚上装正面图": "👚上装正面图url"
        },
        "pageSize": 100,
        "summaryOnly": true
    }' \
    "$BASE_URL$API_ENDPOINT" > /dev/null
end_time=$(date +%s%N)
new_api_time=$((($end_time - $start_time) / 1000000))
log_info "新接口耗时: ${new_api_time}ms"

# 计算性能提升
if [ $old_api_time -gt 0 ]; then
    improvement=$(echo "scale=2; ($old_api_time - $new_api_time) * 100 / $old_api_time" | bc)
    log_success "性能提升: ${improvement}%"
fi

echo ""
log_success "测试脚本执行完成！"

# 使用说明
echo ""
echo "=========================================="
echo "使用说明："
echo "1. 确保服务已启动在 $BASE_URL"
echo "2. 修改测试数据中的 appToken 和 tableId"
echo "3. 根据实际情况调整 recordIds"
echo "4. 运行: chmod +x test-field-mapping-by-record.sh && ./test-field-mapping-by-record.sh"
echo "=========================================="
