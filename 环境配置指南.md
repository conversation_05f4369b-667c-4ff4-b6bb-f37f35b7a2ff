# 紫鸟AI穿衣演示 - 环境配置指南

## 📋 配置文件说明

`.env` 文件包含了Docker Compose运行所需的所有环境变量配置。

## 🔑 必须配置的参数

### 1. 紫鸟API凭据（必须）

```env
# 从紫鸟开放平台获取
ZINIAO_API_APP_ID=您的应用ID
ZINIAO_API_PRIVATE_KEY=您的应用私钥
```

**获取方式：**
1. 访问紫鸟开放平台
2. 注册/登录账号
3. 创建应用获取APP_ID和私钥

### 2. API基础URL（通常不需要修改）

```env
ZINIAO_API_BASE_URL=https://sbappstoreapi.ziniao.com
```

## ⚙️ 常用配置参数

### 应用端口配置

```env
# 默认端口8080，如果被占用可以修改
SERVER_PORT=8080
```

**端口选择建议：**
- `8080` - 默认端口
- `8081` - 备用端口
- `9090` - 管理端口

### JVM内存配置

```env
# 根据服务器配置调整
JAVA_OPTS=-Xmx512m -Xms256m -Djava.security.egd=file:/dev/./urandom -XX:+UseG1GC
```

**内存配置建议：**
- **小型部署** (1-2GB服务器): `-Xmx256m -Xms128m`
- **中型部署** (4-8GB服务器): `-Xmx512m -Xms256m`
- **大型部署** (8GB+服务器): `-Xmx1g -Xms512m`

### 日志级别配置

```env
# 应用日志级别
LOGGING_LEVEL_COM_ZINIAO=INFO

# 根日志级别  
LOGGING_LEVEL_ROOT=WARN
```

**日志级别说明：**
- `DEBUG` - 详细调试信息（开发环境）
- `INFO` - 一般信息（生产环境推荐）
- `WARN` - 警告信息
- `ERROR` - 错误信息

## 🚀 快速配置步骤

### 步骤1：复制配置文件
```bash
cp .env.example .env
```

### 步骤2：编辑配置文件
```bash
# 使用您喜欢的编辑器
vim .env
# 或
nano .env
# 或
code .env
```

### 步骤3：配置API凭据
找到以下行并替换为您的实际值：
```env
ZINIAO_API_APP_ID=your_app_id_here          # 替换为您的APP ID
ZINIAO_API_PRIVATE_KEY=your_private_key_here # 替换为您的私钥
```

### 步骤4：调整其他配置（可选）
根据您的服务器配置调整内存、端口等参数。

### 步骤5：启动服务
```bash
docker-compose up -d
```

## 🔧 高级配置选项

### 性能优化配置

```env
# 最大并发请求数
MAX_CONCURRENT_REQUESTS=10

# AI处理超时时间
AI_PROCESS_TIMEOUT=300

# HTTP连接配置
HTTP_CONNECTION_TIMEOUT=5000
HTTP_READ_TIMEOUT=30000
```

### 安全配置

```env
# 管理端点访问控制
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics

# 健康检查详情显示
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized
```

### 文件上传配置

```env
# 图片上传最大大小（MB）
MAX_UPLOAD_SIZE=10

# 支持的图片格式
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,webp
```

## 🐛 常见配置问题

### 问题1：端口被占用
**症状：** 容器启动失败，提示端口被占用
**解决：** 修改端口配置
```env
SERVER_PORT=8081  # 使用其他端口
```

### 问题2：内存不足
**症状：** 应用启动缓慢或崩溃
**解决：** 调整JVM内存配置
```env
JAVA_OPTS=-Xmx256m -Xms128m  # 减少内存使用
```

### 问题3：API调用失败
**症状：** API返回认证错误
**解决：** 检查API凭据配置
```env
# 确保APP_ID和私钥正确
ZINIAO_API_APP_ID=正确的应用ID
ZINIAO_API_PRIVATE_KEY=正确的私钥
```

## 📊 配置验证

### 验证配置是否正确
```bash
# 启动服务
docker-compose up -d

# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 测试健康检查
curl http://localhost:8080/actuator/health
```

### 验证API连接
```bash
# 测试API端点
curl http://localhost:8080/api/token/app
```

## 🌍 不同环境配置

### 开发环境
```env
DEBUG=true
LOGGING_LEVEL_COM_ZINIAO=DEBUG
SPRING_DEVTOOLS_RESTART_ENABLED=true
```

### 测试环境
```env
DEBUG=false
LOGGING_LEVEL_COM_ZINIAO=INFO
SPRING_PROFILES_ACTIVE=test
```

### 生产环境
```env
DEBUG=false
LOGGING_LEVEL_COM_ZINIAO=WARN
LOGGING_LEVEL_ROOT=ERROR
JAVA_OPTS=-Xmx1g -Xms512m -XX:+UseG1GC
```

## 📚 相关文档

- [快速开始.md](快速开始.md) - 快速启动指南
- [DOCKER使用说明.md](DOCKER使用说明.md) - 详细使用说明
- [项目总结.md](项目总结.md) - 项目完成总结

## 🆘 获取帮助

如果配置过程中遇到问题：
1. 检查配置文件语法是否正确
2. 确认API凭据是否有效
3. 查看容器日志排查问题
4. 参考故障排除文档

---

**提示：** 配置完成后，记得保存文件并重启Docker Compose服务以使配置生效。
