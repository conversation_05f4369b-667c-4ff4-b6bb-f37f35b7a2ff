{"examples": [{"name": "服装图片映射示例", "description": "将服装相关的图片字段映射到对应的URL字段", "request": {"appToken": "your_app_token_here", "tableId": "your_table_id_here", "fieldMapping": {"👚上装正面图": "👚上装正面图url", "👚上装背面图": "👚上装背面图url", "👖下装正面图": "👖下装正面图url", "👖下装背面图": "👖下装背面图url", "👠鞋子图片": "👠鞋子图片url", "👜包包图片": "👜包包图片url"}, "pageSize": 10, "updateBitableWithLocalUrl": true}}, {"name": "产品图片映射示例", "description": "将产品图片字段映射到对应的URL字段", "request": {"appToken": "your_app_token_here", "tableId": "your_table_id_here", "fieldMapping": {"产品主图": "产品主图url", "产品详情图1": "产品详情图1url", "产品详情图2": "产品详情图2url", "产品详情图3": "产品详情图3url", "产品包装图": "产品包装图url"}, "filter": "CurrentValue.[状态] = \"已发布\"", "pageSize": 20, "maxConcurrentDownloads": 3, "downloadTimeout": 60}}, {"name": "用户资料图片映射示例", "description": "将用户相关的图片字段映射到对应的URL字段", "request": {"appToken": "your_app_token_here", "tableId": "your_table_id_here", "fieldMapping": {"用户头像": "用户头像url", "用户背景图": "用户背景图url", "身份证正面": "身份证正面url", "身份证背面": "身份证背面url"}, "recordId": "specific_record_id", "updateBitableWithLocalUrl": true}}, {"name": "房产图片映射示例", "description": "将房产相关的图片字段映射到对应的URL字段", "request": {"appToken": "your_app_token_here", "tableId": "your_table_id_here", "fieldMapping": {"房屋外观图": "房屋外观图url", "客厅图片": "客厅图片url", "卧室图片": "卧室图片url", "厨房图片": "厨房图片url", "卫生间图片": "卫生间图片url", "户型图": "户型图url"}, "filter": "CurrentValue.[房屋状态] = \"待售\"", "pageSize": 5, "downloadTimeout": 90}}, {"name": "车辆图片映射示例", "description": "将车辆相关的图片字段映射到对应的URL字段", "request": {"appToken": "your_app_token_here", "tableId": "your_table_id_here", "fieldMapping": {"车辆正面图": "车辆正面图url", "车辆侧面图": "车辆侧面图url", "车辆内饰图": "车辆内饰图url", "发动机舱图": "发动机舱图url", "行驶证图片": "行驶证图片url"}, "viewId": "specific_view_id", "pageSize": 15, "maxConcurrentDownloads": 2}}, {"name": "只下载不写回示例", "description": "只下载图片到本地，不写回多维表格", "request": {"appToken": "your_app_token_here", "tableId": "your_table_id_here", "fieldMapping": {"临时图片": "临时图片url"}, "updateBitableWithLocalUrl": false, "includeImageDetails": true}}], "curl_commands": [{"name": "基本使用命令", "command": "curl -X POST \"http://localhost:8080/api/feishu/bitable/images/field-mapping-upload\" -H \"Content-Type: application/json\" -d '{\"appToken\": \"your_app_token\", \"tableId\": \"your_table_id\", \"fieldMapping\": {\"👚上装正面图\": \"👚上装正面图url\"}}'"}, {"name": "多字段映射命令", "command": "curl -X POST \"http://localhost:8080/api/feishu/bitable/images/field-mapping-upload\" -H \"Content-Type: application/json\" -d '{\"appToken\": \"your_app_token\", \"tableId\": \"your_table_id\", \"fieldMapping\": {\"👚上装正面图\": \"👚上装正面图url\", \"👚上装背面图\": \"👚上装背面图url\"}, \"pageSize\": 10}'"}, {"name": "指定记录命令", "command": "curl -X POST \"http://localhost:8080/api/feishu/bitable/images/field-mapping-upload\" -H \"Content-Type: application/json\" -d '{\"appToken\": \"your_app_token\", \"tableId\": \"your_table_id\", \"recordId\": \"specific_record_id\", \"fieldMapping\": {\"产品主图\": \"产品主图url\"}}'"}], "field_mapping_patterns": [{"pattern": "图片字段 -> URL字段", "examples": ["产品图片 -> 产品图片url", "用户头像 -> 用户头像url", "商品主图 -> 商品主图url"]}, {"pattern": "带emoji的字段映射", "examples": ["👚上装正面图 -> 👚上装正面图url", "🏠房屋外观 -> 🏠房屋外观url", "🚗车辆图片 -> 🚗车辆图片url"]}, {"pattern": "详细描述字段映射", "examples": ["产品详情图1 -> 产品详情图1url", "房间照片_客厅 -> 房间照片_客厅url", "证件照片_正面 -> 证件照片_正面url"]}], "tips": ["确保目标字段在多维表格中已存在", "目标字段应为文本类型以存储URL", "合理设置并发下载数避免服务器过载", "使用筛选条件可以只处理特定状态的记录", "可以先设置 updateBitableWithLocalUrl=false 测试下载功能", "建议在测试环境先验证字段映射关系的正确性"]}