#!/bin/bash

# 紫鸟AI穿衣演示 - 环境配置助手
# 此脚本帮助您快速配置.env文件

set -e

echo "=== 紫鸟AI穿衣演示 - 环境配置助手 ==="
echo ""

# 输出颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[配置]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[提示]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否存在配置文件
if [ -f ".env" ]; then
    print_warning "发现现有的.env文件"
    read -p "是否要重新配置？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "保持现有配置，退出配置助手"
        exit 0
    fi
fi

print_status "开始配置环境变量..."
echo ""

# 配置选择
echo "请选择配置方式："
echo "1. 简化配置（推荐新手）"
echo "2. 完整配置（高级用户）"
echo "3. 使用现有模板"
echo ""

read -p "请选择 (1-3): " config_choice

case $config_choice in
    1)
        print_status "使用简化配置模板..."
        if [ -f ".env.simple" ]; then
            cp .env.simple .env
        else
            print_error "未找到简化配置模板"
            exit 1
        fi
        ;;
    2)
        print_status "使用完整配置模板..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
        else
            print_error "未找到完整配置模板"
            exit 1
        fi
        ;;
    3)
        print_status "保持现有配置..."
        ;;
    *)
        print_error "无效选择"
        exit 1
        ;;
esac

# 配置API凭据
echo ""
print_status "配置紫鸟API凭据"
echo ""

print_warning "请从紫鸟开放平台获取以下信息："
echo "1. 访问紫鸟开放平台"
echo "2. 注册/登录账号"
echo "3. 创建应用获取APP_ID和私钥"
echo ""

read -p "请输入您的应用ID (APP_ID): " app_id
if [ -z "$app_id" ]; then
    print_warning "未输入APP_ID，将使用默认占位符"
    app_id="your_app_id_here"
fi

read -p "请输入您的应用私钥 (PRIVATE_KEY): " private_key
if [ -z "$private_key" ]; then
    print_warning "未输入私钥，将使用默认占位符"
    private_key="your_private_key_here"
fi

# 更新配置文件
print_status "更新配置文件..."

# 替换APP_ID
sed -i.bak "s/ZINIAO_API_APP_ID=.*/ZINIAO_API_APP_ID=$app_id/" .env

# 替换私钥
sed -i.bak "s/ZINIAO_API_PRIVATE_KEY=.*/ZINIAO_API_PRIVATE_KEY=$private_key/" .env

# 删除备份文件
rm -f .env.bak

# 配置端口
echo ""
read -p "请输入应用端口 (默认8080): " port
if [ -z "$port" ]; then
    port="8080"
fi

sed -i.bak "s/SERVER_PORT=.*/SERVER_PORT=$port/" .env
rm -f .env.bak

# 配置内存
echo ""
echo "请选择内存配置："
echo "1. 小型部署 (256MB) - 适合1-2GB服务器"
echo "2. 中型部署 (512MB) - 适合4-8GB服务器"
echo "3. 大型部署 (1GB) - 适合8GB+服务器"
echo "4. 自定义"
echo ""

read -p "请选择 (1-4): " memory_choice

case $memory_choice in
    1)
        java_opts="-Xmx256m -Xms128m -Djava.security.egd=file:/dev/./urandom"
        ;;
    2)
        java_opts="-Xmx512m -Xms256m -Djava.security.egd=file:/dev/./urandom"
        ;;
    3)
        java_opts="-Xmx1g -Xms512m -Djava.security.egd=file:/dev/./urandom"
        ;;
    4)
        read -p "请输入自定义JVM参数: " java_opts
        if [ -z "$java_opts" ]; then
            java_opts="-Xmx512m -Xms256m -Djava.security.egd=file:/dev/./urandom"
        fi
        ;;
    *)
        java_opts="-Xmx512m -Xms256m -Djava.security.egd=file:/dev/./urandom"
        ;;
esac

# 更新JVM配置（需要转义特殊字符）
escaped_java_opts=$(echo "$java_opts" | sed 's/[[\.*^$()+?{|]/\\&/g')
sed -i.bak "s|JAVA_OPTS=.*|JAVA_OPTS=$escaped_java_opts|" .env
rm -f .env.bak

# 显示配置摘要
echo ""
print_success "配置完成！"
echo ""
print_status "配置摘要："
echo "  • 应用ID: $app_id"
echo "  • 私钥: ${private_key:0:10}..." # 只显示前10个字符
echo "  • 端口: $port"
echo "  • JVM配置: $java_opts"
echo ""

# 验证配置
print_status "验证配置文件..."
if [ -f ".env" ]; then
    print_success "配置文件已创建: .env"
    
    # 检查关键配置
    if grep -q "your_app_id_here" .env; then
        print_warning "检测到默认APP_ID，请确保已配置正确的API凭据"
    fi
    
    if grep -q "your_private_key_here" .env; then
        print_warning "检测到默认私钥，请确保已配置正确的API凭据"
    fi
else
    print_error "配置文件创建失败"
    exit 1
fi

# 下一步提示
echo ""
print_status "下一步操作："
echo "1. 检查配置: cat .env"
echo "2. 启动服务: docker-compose up -d"
echo "3. 查看日志: docker-compose logs -f"
echo "4. 访问应用: http://localhost:$port"
echo ""

print_status "如需修改配置，可以："
echo "• 直接编辑 .env 文件"
echo "• 重新运行此配置助手"
echo "• 参考 环境配置指南.md"
echo ""

print_success "配置助手完成！"
