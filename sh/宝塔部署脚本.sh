#!/bin/bash

# 宝塔面板部署脚本
# 用于在宝塔环境中正确部署和配置应用

echo "=== 宝塔面板部署配置脚本 ==="

# 配置变量
APP_DIR="/www/wwwroot/fs.vwo50.life_998"
JAR_DIR="$APP_DIR/jar"
UPLOADS_DIR="$APP_DIR/uploads"
CONFIG_FILE="$JAR_DIR/application-prod.yml"
JAR_FILE="$JAR_DIR/ziniao-ai-demo-1.0.0.jar"

# 1. 创建必要的目录
echo "1. 创建必要的目录..."
mkdir -p "$JAR_DIR"
mkdir -p "$UPLOADS_DIR"
mkdir -p "$UPLOADS_DIR/2025"

# 2. 设置目录权限
echo "2. 设置目录权限..."
chmod -R 755 "$UPLOADS_DIR"
chown -R www:www "$UPLOADS_DIR"

# 3. 创建生产环境配置文件
echo "3. 创建生产环境配置文件..."
cat > "$CONFIG_FILE" << 'EOF'
# 生产环境配置文件
server:
  port: 18088

# 文件上传配置
file:
  upload:
    # 服务器基础URL
    server-base-url: http://fs.vwo50.life:18088
    # 上传文件存储路径（绝对路径）
    path: /www/wwwroot/fs.vwo50.life_998/uploads/
    # 最大文件大小（字节）200MB
    max-size: 209715200
    # 允许的文件类型
    allowed-types: jpg,jpeg,png,gif,bmp,webp,pdf,doc,docx,txt,zip,rar,mp4,avi,mov,mp3,wav
    # 服务器访问URL前缀
    url-prefix: /uploads/

# 紫鸟API配置
ziniao:
  api:
    base-url: https://sbappstoreapi.ziniao.com
    app-id: 202507121393517203126611968
    private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDERMWtqBpMyLnjLsJ0DS4PtYigGEN1Q+E3oaVe+U5jG9D3BjPf5a6YUAr1mBnLD2LFUtwEV0qQeYQNnWshU7lahKsTkPuVupOgWMMRm5dGghp75bn7zRf+x+zgvSqwpFM1pMd/X/TlX6Ab0aBrgoRu4GdMssf5XlWT8abiraAzFCi7mhO6xEUuRW9Kzy1dYLHnvWuCYNgvkbyYbny/S9DUkFlsHzq2pn99Wxx8ZCqkOHwyKXjv7XfDtR9mqazXdVmie07FbAaOEYqoyZ5Mq/DtELXuhtEtdNXSvwSK9QeC6psL067Yc/CaIfIbVoYk9R5ZzHZpMaLowz8F65nNYuCLAgMBAAECggEAUtm5HKpWl80v8v/dJAIgTI9UIki7yTejTcmVtnbFKT8nwe9DXKNnRXKcfbn8QWS4TBXDPK3gkwZIwTqPa50dJGHWmcbc/QKqqMhvZ/JnNGNdbKm4Ddww4eNhUiloaPKlEYMCl/lYtq7cNoISFvAcmXVvBch+c/znYTFkQlIKyyRcrOhtNkRvCp6eeWSZc7C7oQUpLQDQAju4r/e2m/6BGgiYcGL4DIs5nB0M3M8NKPWfiDMLX5kNMZk3LaygdNlvDNS/vqYHayQW3t1ZpIWLykkmdyrchTo2CP0kpjyyukNcq1dTipCetvxg/uQQjLdG7fGD9aaPsrQwC/IeRRIe2QKBgQDwVGZxnbsNGwzNg5APifPvuKFEiStwbZtIZXLO4pbAQxHR65/rrqg0QenNcMbn2hN76IpWTml20U6XHlCHialHHfCYUA7fRjb7Pz8M3Mo5gpN6t7SqvMSah9hKCSfNp3l4elHd/6HOTqklMU9tznSvmtrvExSlEa+2/GwulWtHnwKBgQDREObmy9bW+hUY7VohDX1BM2fTfHrYDv3ueC13ntPedI1IzMeq57k+CJHTdxrEcPhK5pgF8rr0DPa221or8Yyhe6Hssv6nxjAjGYOtw24xKFFFGBF5qB6pSbS3YpOwCO6AxcC2ofIX3EVXbajAN9sAJSndg2n1o5cqbT/rOLQvlQKBgD2Lb5yi5vbpOXMbwPWpv1Vhc7MIaZ5mA0Eb8bW3YyGjS1n1vENvu9V+F7Crs9RGDX1KxLprZCiwWy7p7Xd4Mmhq62UmA1j8MzJONQhHNmZZ4QipKNQceUCVjzcOdyn8PGg08ugmAbrCfCBxK0UUfN9Hic91lHDdQaFbrRyU2R9DAoGAKwihnsxCksmzYggpTzhpmews6Plsh+C+IEIYidDlp5qyIlQUnXdJSB2XdsHDfsAKO9CvZjoKhYXNmuIX84eq/Opn8EL/7CT9b5wm4LOjkPbNk71ai4IxnYMQcdcWs1uDTHpoKq+3F3Y9x7tIyxg0OhlsPq54NxnPWepXB+IKJOkCgYBlWkaXRdZb63dYkDyKRSaIKz61WtXPdxk16FCCsNyTdkNobiEsfD+QJ7njY98/Hm00LYE5R9fNXq0U+CJEYKlilhc2ab4CH4S6TdKlVld//nPdr3K4N+H8yhvUlxheS1LCIPQYWM8BEhjWSiVvqNnFnbCk1DhbolVj7R1QqhzMvQ==
    timeout: 30000
    token-cache-time: 7200

# 飞书API配置
feishu:
  api:
    base-url: https://open.feishu.cn
    app-id: cli_a8fe3e73bd78d00d
    app-secret: whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU
    timeout: 30000
    token-cache-time: 7000
    connect-timeout: 10000
    read-timeout: 30000

# 日志配置
logging:
  level:
    com.ziniao: DEBUG
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log
    max-size: 10MB
    max-history: 30

# Spring Boot静态资源配置
spring:
  web:
    resources:
      static-locations: file:/www/wwwroot/fs.vwo50.life_998/uploads/
EOF

# 4. 创建启动脚本
echo "4. 创建启动脚本..."
cat > "$JAR_DIR/start.sh" << 'EOF'
#!/bin/bash
cd /www/wwwroot/fs.vwo50.life_998/jar
/www/server/java/jdk1.8.0_371/bin/java \
  -jar \
  -Xmx1024M \
  -Xms256M \
  -Dspring.profiles.active=prod \
  -Dfile.upload.path=/www/wwwroot/fs.vwo50.life_998/uploads/ \
  -Dfile.upload.server-base-url=http://fs.vwo50.life:18088 \
  ziniao-ai-demo-1.0.0.jar \
  --server.port=18088
EOF

chmod +x "$JAR_DIR/start.sh"

# 5. 创建测试脚本
echo "5. 创建测试脚本..."
cat > "$JAR_DIR/test-static-files.sh" << 'EOF'
#!/bin/bash

echo "=== 测试静态文件访问 ==="

# 创建测试图片
TEST_DIR="/www/wwwroot/fs.vwo50.life_998/uploads/test"
mkdir -p "$TEST_DIR"

# 创建一个简单的测试文件
echo "Test Image Content" > "$TEST_DIR/test.txt"

# 测试文件访问
echo "测试文件访问："
echo "文件路径: $TEST_DIR/test.txt"
echo "访问URL: http://fs.vwo50.life:18088/uploads/test/test.txt"

# 检查文件权限
echo ""
echo "文件权限检查："
ls -la "$TEST_DIR/test.txt"

echo ""
echo "目录权限检查："
ls -la "/www/wwwroot/fs.vwo50.life_998/uploads/"
EOF

chmod +x "$JAR_DIR/test-static-files.sh"

# 6. 显示配置信息
echo ""
echo "=== 部署配置完成 ==="
echo "应用目录: $APP_DIR"
echo "JAR目录: $JAR_DIR"
echo "上传目录: $UPLOADS_DIR"
echo "配置文件: $CONFIG_FILE"
echo ""
echo "宝塔面板Java项目启动命令："
echo "/www/server/java/jdk1.8.0_371/bin/java -jar -Xmx1024M -Xms256M -Dspring.profiles.active=prod /www/wwwroot/fs.vwo50.life_998/jar/ziniao-ai-demo-1.0.0.jar --server.port=18088"
echo ""
echo "或者使用启动脚本："
echo "$JAR_DIR/start.sh"
echo ""
echo "测试静态文件访问："
echo "$JAR_DIR/test-static-files.sh"
echo ""
echo "应用访问地址："
echo "- 主应用: http://fs.vwo50.life:18088"
echo "- API文档: http://fs.vwo50.life:18088/doc.html"
echo "- 测试图片: http://fs.vwo50.life:18088/uploads/test/test.txt"

echo ""
echo "=== 部署完成！请重启应用 ==="
