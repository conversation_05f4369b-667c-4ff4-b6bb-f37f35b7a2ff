#!/bin/bash

# 宝塔面板Java项目启动脚本
# 解决静态资源404问题

echo "=== 紫鸟AI Demo 启动脚本 ==="

# 配置变量
APP_DIR="/www/wwwroot/fs.vwo50.life_998"
JAR_DIR="$APP_DIR/jar"
UPLOADS_DIR="$APP_DIR/uploads"
JAR_FILE="$JAR_DIR/ziniao-ai-demo-1.0.0.jar"
JAVA_BIN="/www/server/java/jdk1.8.0_371/bin/java"

# 检查Java是否存在
if [ ! -f "$JAVA_BIN" ]; then
    echo "错误: Java不存在于 $JAVA_BIN"
    exit 1
fi

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在于 $JAR_FILE"
    exit 1
fi

# 创建uploads目录
echo "创建uploads目录..."
mkdir -p "$UPLOADS_DIR"
chmod -R 755 "$UPLOADS_DIR"

# 创建日志目录
mkdir -p "$APP_DIR/logs"
chmod -R 755 "$APP_DIR/logs"

# 切换到JAR目录
cd "$JAR_DIR"

echo "启动应用..."
echo "JAR文件: $JAR_FILE"
echo "上传目录: $UPLOADS_DIR"
echo "服务器地址: http://39.108.93.224:18088"

# 启动应用
exec "$JAVA_BIN" \
    -jar \
    -Xmx1024M \
    -Xms256M \
    -Dfile.encoding=UTF-8 \
    -Djava.awt.headless=true \
    -Djava.security.egd=file:/dev/./urandom \
    -Dfile.upload.path="$UPLOADS_DIR/" \
    -Dfile.upload.server-base-url="http://39.108.93.224:18088" \
    -Dfile.upload.url-prefix="/uploads/" \
    -Dspring.web.resources.static-locations="file:$UPLOADS_DIR/" \
    -Dlogging.file.name="$APP_DIR/logs/ziniao-ai-demo.log" \
    "$JAR_FILE" \
    --server.port=18088 \
    --file.upload.path="$UPLOADS_DIR/" \
    --file.upload.server-base-url="http://39.108.93.224:18088" \
    --file.upload.url-prefix="/uploads/" \
    --spring.web.resources.static-locations="file:$UPLOADS_DIR/" \
    --logging.level.com.ziniao=DEBUG \
    --logging.level.org.springframework.web=DEBUG
