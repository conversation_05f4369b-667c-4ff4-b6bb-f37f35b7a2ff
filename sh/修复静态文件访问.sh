#!/bin/bash

# 修复静态文件访问问题的脚本

echo "=== 修复静态文件访问问题 ==="

# 配置变量
APP_DIR="/www/wwwroot/fs.vwo50.life_998"
UPLOADS_DIR="$APP_DIR/uploads"
NGINX_CONF="/www/server/panel/vhost/nginx/fs.vwo50.life.conf"

# 1. 检查uploads目录
echo "1. 检查uploads目录..."
if [ ! -d "$UPLOADS_DIR" ]; then
    echo "创建uploads目录: $UPLOADS_DIR"
    mkdir -p "$UPLOADS_DIR"
fi

# 设置权限
chmod -R 755 "$UPLOADS_DIR"
chown -R www:www "$UPLOADS_DIR"

# 2. 创建测试文件
echo "2. 创建测试文件..."
TEST_FILE="$UPLOADS_DIR/test.txt"
echo "Test file content - $(date)" > "$TEST_FILE"
chmod 644 "$TEST_FILE"

# 3. 检查现有图片文件
echo "3. 检查现有图片文件..."
EXISTING_IMAGE="$UPLOADS_DIR/2025/07/25/20250725_025105_1543619a.png"
if [ -f "$EXISTING_IMAGE" ]; then
    echo "找到图片文件: $EXISTING_IMAGE"
    ls -la "$EXISTING_IMAGE"
    chmod 644 "$EXISTING_IMAGE"
else
    echo "图片文件不存在: $EXISTING_IMAGE"
    # 创建目录结构
    mkdir -p "$(dirname "$EXISTING_IMAGE")"
    # 创建一个占位文件用于测试
    echo "Placeholder image file" > "$EXISTING_IMAGE"
    chmod 644 "$EXISTING_IMAGE"
fi

# 4. 创建Nginx配置片段
echo "4. 创建Nginx配置建议..."
cat > "$APP_DIR/nginx-static-config.txt" << 'EOF'
# 将以下配置添加到你的Nginx站点配置中

location /uploads/ {
    alias /www/wwwroot/fs.vwo50.life_998/uploads/;
    expires 1h;
    add_header Cache-Control "public, immutable";
    
    # 允许跨域访问
    add_header Access-Control-Allow-Origin *;
    
    # 处理图片文件
    location ~* \.(jpg|jpeg|png|gif|bmp|webp)$ {
        expires 7d;
        add_header Cache-Control "public, immutable";
    }
}

# 代理Java应用（如果需要）
location / {
    proxy_pass http://127.0.0.1:18088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
EOF

# 5. 创建Apache配置片段（如果使用Apache）
cat > "$APP_DIR/apache-static-config.txt" << 'EOF'
# 将以下配置添加到你的Apache站点配置中

Alias /uploads /www/wwwroot/fs.vwo50.life_998/uploads
<Directory "/www/wwwroot/fs.vwo50.life_998/uploads">
    Options Indexes FollowSymLinks
    AllowOverride None
    Require all granted
    
    # 设置缓存
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType image/jpg "access plus 7 days"
        ExpiresByType image/jpeg "access plus 7 days"
        ExpiresByType image/png "access plus 7 days"
        ExpiresByType image/gif "access plus 7 days"
        ExpiresByType image/webp "access plus 7 days"
    </IfModule>
</Directory>

# 代理Java应用
ProxyPass /api/ http://127.0.0.1:18088/api/
ProxyPassReverse /api/ http://127.0.0.1:18088/api/
EOF

# 6. 测试文件访问
echo "6. 测试文件访问..."
echo ""
echo "=== 测试结果 ==="
echo "测试文件路径: $TEST_FILE"
echo "测试文件URL: http://39.108.93.224:18088/uploads/test.txt"
echo ""
echo "图片文件路径: $EXISTING_IMAGE"
echo "图片文件URL: http://39.108.93.224:18088/uploads/2025/07/25/20250725_025105_1543619a.png"
echo ""

# 7. 检查端口是否开放
echo "7. 检查端口状态..."
if command -v netstat >/dev/null 2>&1; then
    echo "检查18088端口:"
    netstat -tlnp | grep :18088 || echo "端口18088未监听"
fi

# 8. 提供解决方案
echo ""
echo "=== 解决方案 ==="
echo "方案1: 使用Nginx配置（推荐）"
echo "  - 在宝塔面板中编辑网站配置"
echo "  - 添加 $APP_DIR/nginx-static-config.txt 中的配置"
echo "  - 重载Nginx配置"
echo ""
echo "方案2: 使用Apache配置"
echo "  - 在宝塔面板中编辑网站配置"
echo "  - 添加 $APP_DIR/apache-static-config.txt 中的配置"
echo "  - 重载Apache配置"
echo ""
echo "方案3: 直接通过Java应用访问"
echo "  - 确保Java应用的静态资源配置正确"
echo "  - 重启Java应用"
echo ""

# 9. 创建快速测试脚本
cat > "$APP_DIR/test-static-access.sh" << 'EOF'
#!/bin/bash
echo "=== 测试静态文件访问 ==="
echo "测试1: 直接文件访问"
curl -I "http://39.108.93.224:18088/uploads/test.txt" 2>/dev/null | head -1

echo "测试2: 图片文件访问"
curl -I "http://39.108.93.224:18088/uploads/2025/07/25/20250725_025105_1543619a.png" 2>/dev/null | head -1

echo "测试3: 检查文件是否存在"
ls -la "/www/wwwroot/fs.vwo50.life_998/uploads/2025/07/25/20250725_025105_1543619a.png"
EOF

chmod +x "$APP_DIR/test-static-access.sh"

echo "修复脚本执行完成！"
echo "请运行测试脚本: $APP_DIR/test-static-access.sh"
