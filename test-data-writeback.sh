#!/bin/bash

# 测试数据回写接口

echo "=== 测试数据回写接口 ==="
echo ""

BASE_URL="http://localhost:8080"
TASK_ID="1945877291232894976"

echo "🔍 测试1: 基本数据回写功能"
echo "接口: POST $BASE_URL/api/clothing/writeback/$TASK_ID"
echo ""

# 测试基本回写功能
response=$(curl -s -X POST "$BASE_URL/api/clothing/writeback/$TASK_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "fields": {
      "AI处理状态": "已完成",
      "结果图片": "https://example.com/result.jpg",
      "处理时间": "2024-01-15 10:30:00",
      "置信度": 0.95
    },
    "writeBackType": "ai_result",
    "remark": "AI穿衣处理完成，回写结果"
  }')

echo "响应结果:"
echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
echo ""

# 检查响应状态
if echo "$response" | grep -q '"code":200'; then
    echo "✅ 测试1通过: 基本回写功能正常"
else
    echo "❌ 测试1失败: 基本回写功能异常"
fi

echo ""
echo "🔍 测试2: 多字段回写功能"

# 测试多字段回写
response2=$(curl -s -X POST "$BASE_URL/api/clothing/writeback/test_multi_fields" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "fields": {
      "处理状态": "成功",
      "结果图片1": "https://example.com/result1.jpg",
      "结果图片2": "https://example.com/result2.jpg",
      "处理耗时": "30秒",
      "质量评分": 4.5,
      "备注": "处理效果良好"
    },
    "writeBackType": "ai_result",
    "remark": "批量回写AI处理结果"
  }')

echo "响应结果:"
echo "$response2" | python3 -m json.tool 2>/dev/null || echo "$response2"
echo ""

# 检查响应状态
if echo "$response2" | grep -q '"code":200'; then
    echo "✅ 测试2通过: 多字段回写功能正常"
else
    echo "❌ 测试2失败: 多字段回写功能异常"
fi

echo ""
echo "🔍 测试3: 手动更新类型"

# 测试手动更新类型
response3=$(curl -s -X POST "$BASE_URL/api/clothing/writeback/manual_update_001" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "fields": {
      "审核状态": "已审核",
      "审核人": "张三",
      "审核意见": "效果良好，可以使用"
    },
    "writeBackType": "manual_update",
    "remark": "人工审核结果更新"
  }')

echo "响应结果:"
echo "$response3" | python3 -m json.tool 2>/dev/null || echo "$response3"
echo ""

# 检查响应状态
if echo "$response3" | grep -q '"code":200'; then
    echo "✅ 测试3通过: 手动更新类型正常"
else
    echo "❌ 测试3失败: 手动更新类型异常"
fi

echo ""
echo "🔍 测试4: 参数验证测试"

# 测试缺少必需参数
response4=$(curl -s -X POST "$BASE_URL/api/clothing/writeback/validation_test" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "fields": {}
  }')

echo "参数验证测试响应:"
echo "$response4" | python3 -m json.tool 2>/dev/null || echo "$response4"
echo ""

# 检查是否正确返回错误
if echo "$response4" | grep -q '"code":400\|"code":500'; then
    echo "✅ 测试4通过: 参数验证正常"
else
    echo "❌ 测试4失败: 参数验证异常"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📋 接口使用说明:"
echo "1. 接口地址: POST /api/clothing/writeback/{taskId}"
echo "2. 必需参数: appToken, tableId, recordId, fields"
echo "3. 可选参数: writeBackType, remark"
echo "4. 支持的回写类型: ai_result, manual_update, batch_update"
echo ""
echo "📖 详细文档请参考: DATA_WRITEBACK_API_GUIDE.md"
