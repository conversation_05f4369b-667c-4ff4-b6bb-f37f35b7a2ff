#!/bin/bash

# 多图片支持测试脚本
# 测试 /api/feishu/bitable/images/field-mapping-by-record 接口的多图片处理功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
BASE_URL="http://localhost:8080"
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}=== 多图片支持功能测试 ===${NC}"
echo ""

# 检查服务是否运行
echo -e "${YELLOW}步骤1: 检查服务状态${NC}"
if curl -s "$BASE_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 服务运行正常${NC}"
else
    echo -e "${RED}✗ 服务未运行，请先启动服务${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}步骤2: 获取表格记录信息${NC}"

# 获取记录信息
RECORDS_RESPONSE=$(curl -s -X POST "$BASE_URL/api/feishu/bitable/records/info" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"pageSize\": 3
  }")

echo "获取到的记录信息："
echo "$RECORDS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RECORDS_RESPONSE"

# 提取第一个记录ID
FIRST_RECORD_ID=$(echo "$RECORDS_RESPONSE" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('records'):
        print(data['records'][0]['recordId'])
    else:
        print('')
except:
    print('')
" 2>/dev/null)

echo ""
echo -e "${YELLOW}步骤3: 测试多图片字段映射处理${NC}"

if [ ! -z "$FIRST_RECORD_ID" ]; then
    echo "使用记录ID: $FIRST_RECORD_ID"
    echo ""
    
    # 测试多图片处理
    echo -e "${BLUE}测试场景1: 处理包含多张图片的字段${NC}"
    
    MULTI_IMAGE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/feishu/bitable/images/field-mapping-by-record" \
      -H "Content-Type: application/json" \
      -d "{
        \"appToken\": \"$APP_TOKEN\",
        \"tableId\": \"$TABLE_ID\",
        \"viewId\": \"$VIEW_ID\",
        \"recordIds\": [\"$FIRST_RECORD_ID\"],
        \"fieldMapping\": {
          \"👚上装正面图\": \"👚上装正面图url\",
          \"👚上装背面图\": \"👚上装背面图url\",
          \"使用垫图粘贴\": \"使用垫图粘贴url\"
        },
        \"updateBitableWithLocalUrl\": true,
        \"summaryOnly\": false,
        \"processId\": \"multi_image_test_$(date +%s)\"
      }")
    
    echo "多图片处理响应："
    echo "$MULTI_IMAGE_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$MULTI_IMAGE_RESPONSE"
    
    # 检查是否成功
    SUCCESS=$(echo "$MULTI_IMAGE_RESPONSE" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print('true' if data.get('success') else 'false')
except:
    print('false')
" 2>/dev/null)
    
    if [ "$SUCCESS" = "true" ]; then
        echo -e "${GREEN}✓ 多图片处理成功${NC}"
        
        # 提取处理统计信息
        PROCESSED_RECORDS=$(echo "$MULTI_IMAGE_RESPONSE" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('data'):
        d = data['data']
        print(f'处理记录数: {d.get(\"processedRecords\", 0)}')
        print(f'图片总数: {d.get(\"totalImages\", 0)}')
        print(f'成功下载: {d.get(\"successfulDownloads\", 0)}')
        print(f'下载失败: {d.get(\"failedDownloads\", 0)}')
except:
    pass
" 2>/dev/null)
        
        echo "$PROCESSED_RECORDS"
        
    else
        echo -e "${RED}✗ 多图片处理失败${NC}"
    fi
    
else
    echo -e "${RED}未能获取到记录ID，跳过测试${NC}"
fi

echo ""
echo -e "${YELLOW}步骤4: 验证回写的URL格式${NC}"

if [ ! -z "$FIRST_RECORD_ID" ]; then
    # 重新获取记录信息，查看回写的URL
    UPDATED_RECORDS_RESPONSE=$(curl -s -X POST "$BASE_URL/api/feishu/bitable/records/info" \
      -H "Content-Type: application/json" \
      -d "{
        \"appToken\": \"$APP_TOKEN\",
        \"tableId\": \"$TABLE_ID\",
        \"viewId\": \"$VIEW_ID\",
        \"pageSize\": 1,
        \"recordIds\": [\"$FIRST_RECORD_ID\"]
      }")
    
    echo "更新后的记录信息："
    echo "$UPDATED_RECORDS_RESPONSE" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('data', {}).get('records'):
        record = data['data']['records'][0]
        fields = record.get('fields', {})
        
        # 检查URL字段
        url_fields = [k for k in fields.keys() if 'url' in k.lower()]
        
        print('URL字段内容:')
        for field_name in url_fields:
            field_value = fields.get(field_name)
            if field_value:
                print(f'  {field_name}: {field_value}')
                
                # 检查是否包含多个链接
                if isinstance(field_value, str):
                    if '|' in field_value or '<a href=' in field_value:
                        print(f'    ✓ 检测到多图片链接格式')
                    elif field_value.startswith('http'):
                        print(f'    ✓ 检测到单图片URL格式')
                    else:
                        print(f'    ? 未知格式')
        
    else:
        print('无法获取记录信息')
except Exception as e:
    print(f'解析失败: {e}')
" 2>/dev/null
    
else
    echo -e "${RED}未能获取到记录ID，跳过验证${NC}"
fi

echo ""
echo -e "${YELLOW}步骤5: 测试不同的字段映射组合${NC}"

# 测试只有一张图片的情况
echo -e "${BLUE}测试场景2: 处理单张图片字段${NC}"

if [ ! -z "$FIRST_RECORD_ID" ]; then
    SINGLE_IMAGE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/feishu/bitable/images/field-mapping-by-record" \
      -H "Content-Type: application/json" \
      -d "{
        \"appToken\": \"$APP_TOKEN\",
        \"tableId\": \"$TABLE_ID\",
        \"viewId\": \"$VIEW_ID\",
        \"recordIds\": [\"$FIRST_RECORD_ID\"],
        \"fieldMapping\": {
          \"👚上装正面图\": \"单图测试url\"
        },
        \"updateBitableWithLocalUrl\": true,
        \"summaryOnly\": true,
        \"processId\": \"single_image_test_$(date +%s)\"
      }")
    
    echo "单图片处理响应（摘要模式）："
    echo "$SINGLE_IMAGE_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$SINGLE_IMAGE_RESPONSE"
fi

echo ""
echo -e "${GREEN}=== 多图片支持功能测试完成 ===${NC}"
echo ""
echo -e "${YELLOW}测试总结:${NC}"
echo "1. ✓ 验证了多图片字段的处理能力"
echo "2. ✓ 测试了URL回写格式（单图片 vs 多图片）"
echo "3. ✓ 验证了字段映射的灵活性"
echo "4. ✓ 测试了摘要模式和详细模式"
echo ""
echo -e "${BLUE}多图片URL格式说明:${NC}"
echo "- 单张图片: 直接返回URL字符串"
echo "- 多张图片: 返回HTML链接格式，用 | 分隔"
echo "- 格式示例: <a href=\"url1\" target=\"_blank\">图片1</a> | <a href=\"url2\" target=\"_blank\">图片2</a>"
echo ""
echo -e "${YELLOW}注意事项:${NC}"
echo "- 飞书表格支持HTML格式的链接，用户可以直接点击跳转"
echo "- 每个链接都会在新标签页中打开（target=\"_blank\"）"
echo "- 图片名称会自动从原始文件名中提取"
