# SDK更新说明

## 🔄 SDK版本更新

项目已成功更新为使用紫鸟官方SDK `sdk-java-5.0.6.jar`。

## 📋 更新内容

### 1. 依赖配置更新

**之前（Maven仓库依赖）:**
```xml
<dependency>
    <groupId>com.gitee.sop</groupId>
    <artifactId>sop-sdk-java</artifactId>
    <version>1.9.5</version>
</dependency>
```

**现在（本地JAR依赖）:**
```xml
<dependency>
    <groupId>com.ziniao</groupId>
    <artifactId>sdk-java</artifactId>
    <version>5.0.6</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/sdk-java-5.0.6.jar</systemPath>
</dependency>
```

### 2. 包名更新

**之前的包名:**
```java
import com.gitee.sop.sdk.client.OpenClient;
import com.gitee.sop.sdk.common.RequestMethod;
import com.gitee.sop.sdk.request.CommonRequest;
import com.gitee.sop.sdk.response.CommonResponse;
```

**现在的包名:**
```java
import com.fzzixun.openapi.sdk.client.OpenClient;
import com.fzzixun.openapi.sdk.common.RequestMethod;
import com.fzzixun.openapi.sdk.request.CommonRequest;
import com.fzzixun.openapi.sdk.response.CommonResponse;
```

### 3. 更新的文件

以下文件已更新包名：
- `src/main/java/com/ziniao/service/TokenService.java`
- `src/main/java/com/ziniao/service/ClothingService.java`
- `pom.xml`

## ✅ 验证更新

### 1. 检查SDK文件
```bash
ls -la sdk-java-5.0.6.jar
```

### 2. 验证包结构
```bash
jar -tf sdk-java-5.0.6.jar | grep -E "(OpenClient|CommonRequest|CommonResponse|RequestMethod)"
```

输出应该包含：
```
com/fzzixun/openapi/sdk/client/OpenClient.class
com/fzzixun/openapi/sdk/common/RequestMethod.class
com/fzzixun/openapi/sdk/request/CommonRequest.class
com/fzzixun/openapi/sdk/response/CommonResponse.class
```

### 3. 编译测试
```bash
# 简单编译测试
./test-compile.sh

# 或使用Maven
mvn clean compile
```

## 🚀 启动应用

更新完成后，按照以下步骤启动应用：

1. **配置API密钥**
   ```yaml
   # src/main/resources/application.yml
   ziniao:
     api:
       app-id: your_app_id_here
       private-key: your_private_key_here
   ```

2. **启动应用**
   ```bash
   mvn spring-boot:run
   ```

3. **访问文档**
   - API文档: http://localhost:8080/doc.html
   - Swagger UI: http://localhost:8080/swagger-ui/

## 🔧 API使用

SDK更新后，API调用方式保持不变：

```bash
# 获取令牌
curl -X GET "http://localhost:8080/api/token/app"

# AI穿衣处理
curl -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://example.com/person.jpg",
    "clothingImage": "https://example.com/clothing.jpg",
    "clothingType": "上衣"
  }'
```

## ⚠️ 注意事项

1. **SDK文件位置**: 确保 `sdk-java-5.0.6.jar` 在项目根目录
2. **版本兼容性**: 新SDK版本可能有API变化，请参考官方文档
3. **依赖管理**: 使用 `system` scope 的依赖在打包时需要特殊处理

## 🐛 故障排除

### 1. 找不到SDK类
- 检查 `sdk-java-5.0.6.jar` 是否在项目根目录
- 验证pom.xml中的systemPath配置

### 2. 编译错误
- 确认包名已更新为 `com.fzzixun.openapi.sdk.*`
- 检查import语句是否正确

### 3. 运行时错误
- 确保API密钥配置正确
- 检查网络连接到紫鸟API服务器

## 📞 支持

如有问题，请：
1. 查看项目README.md
2. 检查API文档
3. 联系技术支持
