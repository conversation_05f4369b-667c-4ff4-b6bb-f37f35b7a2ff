# 使用说明

## 快速开始

### 1. 配置API密钥

编辑文件 `src/main/java/com/ziniao/config/ApiConfig.java`，将以下内容替换为您的实际配置：

```java
// 您的API Key - 请替换为实际的API Key
public static final String API_KEY = "your_api_key_here";

// 您的App ID - 请替换为实际的App ID  
public static final String APP_ID = "your_app_id_here";
```

### 2. 编译项目

如果您有Maven：
```bash
mvn clean compile
```

如果没有Maven，使用提供的编译脚本：
```bash
./compile.sh
```

### 3. 运行Demo

使用Maven：
```bash
mvn exec:java -Dexec.mainClass="com.ziniao.demo.ClothingDemo"
```

使用编译脚本编译后：
```bash
java -cp "target/classes:lib/*" com.ziniao.demo.ClothingDemo
```

## 代码示例

### 基本使用

```java
import com.ziniao.model.ClothingRequest;
import com.ziniao.model.ClothingResponse;
import com.ziniao.service.ZiniaoApiService;

// 创建API服务实例
ZiniaoApiService apiService = new ZiniaoApiService();

// 创建请求
ClothingRequest request = new ClothingRequest();
request.setPersonImage("https://example.com/person.jpg");  // 人物图片
request.setClothingImage("https://example.com/shirt.jpg"); // 服装图片
request.setClothingType("上衣");                           // 服装类型

// 调用API
ClothingResponse response = apiService.processClothing(request);

// 处理结果
if (response.isSuccess()) {
    System.out.println("处理成功！");
    System.out.println("结果图片: " + response.getData().getResultImage());
} else {
    System.out.println("处理失败: " + response.getMessage());
}
```

### 异步处理

```java
// 提交任务
ClothingResponse initialResponse = apiService.processClothing(request);
String taskId = initialResponse.getData().getTaskId();

// 等待任务完成
ClothingResponse finalResponse = apiService.waitForTaskCompletion(
    taskId, 
    300000, // 最大等待5分钟
    5000    // 每5秒轮询一次
);
```

## 重要提醒

1. **API密钥安全**：请妥善保管您的API密钥，不要将其提交到版本控制系统中。

2. **图片格式**：
   - 支持URL链接或base64编码
   - 请确保图片格式符合API要求

3. **服装类型**：
   - "上衣" - 上装类服装
   - "下装" - 下装类服装
   - 具体支持的类型请参考官方文档

4. **错误处理**：
   - 检查网络连接
   - 验证API密钥和App ID
   - 确认图片URL可访问

## 故障排除

### 编译错误
- 确保安装了JDK 8或更高版本
- 检查网络连接（下载依赖时需要）

### 运行时错误
- 检查API密钥配置
- 确认图片URL有效
- 查看日志文件 `logs/ziniao-api.log`

### 网络问题
- 检查防火墙设置
- 确认可以访问 `https://open.ziniao.com`

## 联系支持

如有问题，请参考：
- 紫鸟开放平台文档
- 或联系技术支持
