# 🚀 增强版字段映射图片上传接口使用指南

## 📋 接口概述

新的增强版接口 `/api/feishu/bitable/images/field-mapping-upload-enhanced` 完全解决了原接口的限制问题：

### ✅ 解决的问题

1. **突破500行限制**：通过智能分页自动处理超过500行的数据
2. **精确行ID处理**：支持指定特定记录ID或行索引进行处理
3. **数据量优化**：支持摘要模式、批处理等性能优化
4. **完全重构**：全新的架构设计，不影响老接口使用

### 🆚 与原接口对比

| 功能特性 | 原接口 | 增强版接口 |
|---------|--------|-----------|
| 数据量限制 | 500行 | 无限制（智能分页） |
| 行ID支持 | 无 | ✅ 支持记录ID和行索引 |
| 分页处理 | 手动 | ✅ 自动智能分页 |
| 性能优化 | 基础 | ✅ 多种优化选项 |
| 错误处理 | 基础 | ✅ 详细错误信息和统计 |
| 批量处理 | 无 | ✅ 支持批量和范围处理 |

## 🎯 核心功能

### 1. 智能分页处理
自动处理超过500行的数据，无需手动分页：

```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url",
    "👚上装背面图": "👚上装背面图url"
  },
  "enableSmartPaging": true,
  "pageSize": 200,
  "maxPages": 0
}
```

### 2. 精确行ID处理
支持三种精确处理方式：

#### 方式1：指定记录ID列表
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "targetRecordIds": ["recqwIwhc6", "recABC123", "recXYZ789"]
}
```

#### 方式2：指定行索引列表
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "targetRowIndexes": [1, 5, 10, 15, 20]
}
```

#### 方式3：指定行范围
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "rowRange": {
    "start": 100,
    "end": 200
  }
}
```

### 3. 性能优化配置

#### 摘要模式（大数据量推荐）
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "summaryOnly": true,
  "batchSize": 100,
  "maxConcurrentDownloads": 5
}
```

#### 跳过已有URL（避免重复处理）
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "skipExistingUrls": true,
  "continueOnError": true
}
```

## 📊 响应数据结构

增强版接口提供详细的处理统计：

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalRecords": 1500,
    "processedRecords": 1450,
    "skippedRecords": 50,
    "failedRecords": 0,
    "totalImages": 2900,
    "successfulDownloads": 2850,
    "failedDownloads": 50,
    "successfulUpdates": 2850,
    "failedUpdates": 0,
    "pagesProcessed": 8,
    "hasMoreData": false,
    "stoppedByMaxPages": false,
    "totalProcessingTime": 45000,
    "avgRecordProcessingTime": 31.03,
    "avgImageDownloadTime": 15.79,
    "processId": "enhanced_1704067200000",
    "startTime": "Mon Jan 01 12:00:00 CST 2024",
    "endTime": "Mon Jan 01 12:00:45 CST 2024",
    "fieldMappingStats": {
      "👚上装正面图": {
        "sourceField": "👚上装正面图",
        "targetField": "👚上装正面图url",
        "recordsWithImages": 1450,
        "successfulDownloads": 1425,
        "failedDownloads": 25,
        "successfulUpdates": 1425,
        "failedUpdates": 0
      }
    },
    "errors": [],
    "warnings": ["部分图片下载超时，已跳过"]
  }
}
```

## 🛠️ 使用场景

### 场景1：处理大量数据（超过500行）
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url",
      "👖下装正面图": "👖下装正面图url"
    },
    "enableSmartPaging": true,
    "pageSize": 300,
    "maxPages": 10,
    "summaryOnly": true,
    "processId": "batch_large_data_001"
  }'
```

### 场景2：处理特定行（基于行号）
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "targetRowIndexes": [501, 502, 503, 504, 505],
    "includeImageDetails": true,
    "processId": "specific_rows_001"
  }'
```

### 场景3：处理特定记录ID
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "targetRecordIds": ["recqwIwhc6", "recABC123"],
    "skipExistingUrls": true,
    "continueOnError": true,
    "processId": "specific_records_001"
  }'
```

### 场景4：批量范围处理
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "fieldMapping": {
      "使用垫图粘贴": "使用垫图粘贴url"
    },
    "rowRange": {
      "start": 1000,
      "end": 1500
    },
    "batchSize": 50,
    "maxConcurrentDownloads": 3,
    "downloadTimeout": 60,
    "processId": "range_processing_001"
  }'
```

## ⚙️ 配置参数详解

### 核心参数
- `appToken`: 飞书应用Token（必填）
- `tableId`: 数据表ID（必填）
- `fieldMapping`: 字段映射关系（必填）

### 目标选择参数（三选一）
- `targetRecordIds`: 指定记录ID列表
- `targetRowIndexes`: 指定行索引列表（从1开始）
- `rowRange`: 指定行范围 `{"start": 1, "end": 100}`

### 分页参数
- `enableSmartPaging`: 启用智能分页（默认true）
- `pageSize`: 每页记录数（默认200，最大500）
- `maxPages`: 最大处理页数（0表示不限制）

### 性能参数
- `summaryOnly`: 仅返回摘要（默认false）
- `batchSize`: 批处理大小（默认50）
- `downloadTimeout`: 下载超时时间（默认30秒）
- `maxConcurrentDownloads`: 最大并发下载数（默认3）

### 功能参数
- `updateBitableWithLocalUrl`: 是否回写本地URL（默认true）
- `skipExistingUrls`: 跳过已有URL（默认true）
- `includeImageDetails`: 包含图片详情（默认true）
- `continueOnError`: 出错时继续处理（默认true）

### 高级参数
- `processId`: 自定义处理标识
- `verboseLogging`: 详细日志（默认false）
- `priority`: 处理优先级1-10（默认5）

## 🔄 迁移指南

### 从原接口迁移到增强版

#### 原接口调用
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "pageSize": 100
}
```

#### 增强版等效调用
```json
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "enableSmartPaging": false,
  "pageSize": 100
}
```

## 🚨 注意事项

1. **兼容性**：增强版接口与原接口完全独立，不会影响原接口使用
2. **性能**：处理大量数据时建议使用 `summaryOnly: true`
3. **错误处理**：建议设置 `continueOnError: true` 以提高容错性
4. **监控**：使用 `processId` 参数便于日志追踪和问题排查
5. **限制**：单次请求建议不超过5000行数据，超大数据集建议分批处理

## 📈 性能建议

### 大数据量处理（>1000行）
```json
{
  "enableSmartPaging": true,
  "pageSize": 500,
  "summaryOnly": true,
  "batchSize": 100,
  "maxConcurrentDownloads": 5,
  "skipExistingUrls": true
}
```

### 精确处理（<100行）
```json
{
  "includeImageDetails": true,
  "maxConcurrentDownloads": 3,
  "downloadTimeout": 60,
  "verboseLogging": true
}
```

### 生产环境推荐配置
```json
{
  "enableSmartPaging": true,
  "pageSize": 300,
  "maxPages": 20,
  "summaryOnly": false,
  "batchSize": 50,
  "maxConcurrentDownloads": 3,
  "downloadTimeout": 45,
  "skipExistingUrls": true,
  "continueOnError": true,
  "processId": "prod_batch_" + timestamp
}
```
