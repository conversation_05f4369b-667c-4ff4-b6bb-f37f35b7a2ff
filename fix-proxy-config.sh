#!/bin/bash

# 修复图片代理配置问题

echo "🔧 修复图片代理配置问题"
echo "========================"

# 配置变量
APP_NAME="ziniao-ai-demo"
CONFIG_FILE="application-prod.yml"
LOG_FILE="/www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log"

echo ""
echo "1. 检查当前应用状态"
echo "-------------------"
echo "🔍 查找Java进程:"
JAVA_PID=$(ps aux | grep java | grep -v grep | awk '{print $2}')
if [ -n "$JAVA_PID" ]; then
    echo "✅ 找到Java进程: $JAVA_PID"
    ps aux | grep java | grep -v grep
else
    echo "❌ 未找到Java进程"
fi

echo ""
echo "2. 验证配置文件"
echo "---------------"
if [ -f "$CONFIG_FILE" ]; then
    echo "✅ 配置文件存在: $CONFIG_FILE"
    echo ""
    echo "🔧 当前图片代理配置:"
    grep -A 15 "# 图片代理访问配置" "$CONFIG_FILE"
else
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

echo ""
echo "3. 检查配置是否生效"
echo "-------------------"
if [ -f "$LOG_FILE" ]; then
    echo "📋 查找最新的初始化日志:"
    tail -n 1000 "$LOG_FILE" | grep -E "(图片代理功能启用状态|imageProxyEnabled)" | tail -5
    
    echo ""
    echo "📋 查找最新的配置加载日志:"
    tail -n 1000 "$LOG_FILE" | grep -E "(Started.*Application|Configuration.*loaded)" | tail -3
else
    echo "❌ 日志文件不存在: $LOG_FILE"
fi

echo ""
echo "4. 强制重启应用"
echo "---------------"
if [ -n "$JAVA_PID" ]; then
    echo "🛑 停止当前Java进程: $JAVA_PID"
    kill -9 $JAVA_PID
    sleep 3
    
    # 确认进程已停止
    if ps -p $JAVA_PID > /dev/null 2>&1; then
        echo "❌ 进程仍在运行，强制终止"
        kill -9 $JAVA_PID
        sleep 2
    else
        echo "✅ 进程已停止"
    fi
fi

echo ""
echo "🚀 启动应用（使用prod配置）:"
cd /www/wwwroot/fs.vwo50.life_998

# 确保使用正确的配置文件
if [ -f "$APP_NAME.jar" ]; then
    echo "✅ 找到JAR文件: $APP_NAME.jar"
    
    # 启动应用，明确指定配置文件
    nohup java -jar \
        -Dspring.profiles.active=prod \
        -Dspring.config.location=classpath:/application.yml,file:./application-prod.yml \
        -Dserver.port=18088 \
        "$APP_NAME.jar" > app.log 2>&1 &
    
    NEW_PID=$!
    echo "🎯 应用已启动，PID: $NEW_PID"
    
    # 等待应用启动
    echo "⏳ 等待应用启动..."
    sleep 10
    
    # 检查应用是否成功启动
    if ps -p $NEW_PID > /dev/null 2>&1; then
        echo "✅ 应用启动成功"
    else
        echo "❌ 应用启动失败，检查日志:"
        tail -n 20 app.log
        exit 1
    fi
else
    echo "❌ 未找到JAR文件: $APP_NAME.jar"
    ls -la *.jar 2>/dev/null || echo "当前目录没有JAR文件"
    exit 1
fi

echo ""
echo "5. 验证配置是否生效"
echo "-------------------"
echo "⏳ 等待配置加载..."
sleep 5

echo "📋 检查新的初始化日志:"
tail -n 100 "$LOG_FILE" | grep -E "(飞书多维表格服务初始化|图片代理功能启用状态|ImageProxyService注入状态)" | tail -10

echo ""
echo "6. 测试配置"
echo "-----------"
echo "🧪 测试健康检查:"
curl -s -w "HTTP状态码: %{http_code}\n" "http://39.108.93.224:18088/actuator/health" 2>/dev/null || echo "❌ 健康检查失败"

echo ""
echo "🧪 测试代理URL生成接口:"
curl -X POST \
  -w "\nHTTP状态码: %{http_code}\n" \
  -d "filePath=/uploads/2025/07/26/test.png" \
  "http://39.108.93.224:18088/api/image-proxy/generate-url" 2>/dev/null || echo "❌ 代理URL生成接口失败"

echo ""
echo "7. 创建测试脚本"
echo "---------------"
cat > test-specific-fields-after-fix.sh << 'EOF'
#!/bin/bash
echo "🧪 测试修复后的特定图片字段接口"
echo "================================="

# 替换为您的实际参数
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recuRlQ3eEFJEB",
    "updateBitableWithLocalUrl": true
  }' \
  "http://39.108.93.224:18088/api/feishu/bitable/specific-image-fields" | jq '.'

echo ""
echo "🔍 检查返回的localAccessUrl是否为代理格式:"
echo "期望: http://39.108.93.224:18088/api/image-proxy/id/xxxxx"
echo "不期望: http://39.108.93.224:18088/uploads/..."
EOF

chmod +x test-specific-fields-after-fix.sh
echo "📝 已创建测试脚本: test-specific-fields-after-fix.sh"

echo ""
echo "8. 实时监控"
echo "-----------"
echo "📋 使用以下命令监控日志:"
echo "tail -f '$LOG_FILE' | grep -E '(生成图片URL|imageProxyEnabled|代理URL)'"

echo ""
echo "✅ 修复脚本执行完成"
echo ""
echo "🎯 预期结果:"
echo "   1. 应用重启成功"
echo "   2. 日志显示: imageProxyEnabled: true"
echo "   3. 测试接口返回代理URL格式"
echo ""
echo "📞 如果问题仍然存在:"
echo "   1. 检查 app.log 中的启动错误"
echo "   2. 确认配置文件路径正确"
echo "   3. 运行 test-specific-fields-after-fix.sh 测试"
echo ""
echo "🔗 相关命令:"
echo "   - 查看启动日志: tail -f app.log"
echo "   - 查看应用日志: tail -f '$LOG_FILE'"
echo "   - 测试接口: ./test-specific-fields-after-fix.sh"
