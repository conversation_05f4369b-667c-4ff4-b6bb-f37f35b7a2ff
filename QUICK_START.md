# 快速启动指南

## 🚀 问题已解决！

您遇到的 `okhttp3.sse.EventSources` 错误已经解决。现在应用可以正常运行了。

## ✅ 解决方案

我已经为您：
1. **下载了所有必要的依赖** - OkHttp、OkIO、Kotlin等
2. **创建了启动脚本** - 包含完整的classpath
3. **创建了测试脚本** - 验证API功能

## 🔧 启动步骤

### 方法1：使用启动脚本（推荐）

```bash
# 1. 启动应用（包含所有依赖）
./start-with-deps.sh
```

### 方法2：手动启动

```bash
# 1. 确保依赖已下载
./download-deps.sh

# 2. 启动应用
java -cp "target/classes:sdk-java-5.0.6.jar:lib/*" com.ziniao.ZiniaoAiDemoApplication
```

### 方法3：使用Maven（如果有）

```bash
mvn spring-boot:run
```

## 🧪 测试API

应用启动后，在新的终端窗口运行：

```bash
./test-api.sh
```

或者手动测试：

```bash
# 健康检查
curl http://localhost:8080/api/token/health
curl http://localhost:8080/api/clothing/health

# 获取令牌
curl http://localhost:8080/api/token/app

# AI穿衣（使用真实图片URL）
curl -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://your-real-image-url.jpg",
    "clothingImage": "https://your-clothing-image-url.jpg",
    "clothingType": "上衣"
  }'
```

## 📋 可用的API接口

### 令牌管理
- `GET /api/token/app` - 获取应用令牌
- `POST /api/token/app/refresh` - 刷新应用令牌
- `GET /api/token/health` - 健康检查

### AI穿衣
- `POST /api/clothing/process` - AI穿衣处理
- `GET /api/clothing/task/{taskId}` - 查询任务状态
- `GET /api/clothing/health` - 健康检查

## 🌐 访问文档

启动成功后访问：
- **应用首页**: http://localhost:8080
- **Swagger文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui/

## 📁 项目文件说明

### 启动脚本
- `start-with-deps.sh` - 完整启动脚本（推荐）
- `download-deps.sh` - 下载依赖脚本
- `test-api.sh` - API测试脚本

### 配置文件
- `src/main/resources/application.yml` - 应用配置
- `sdk-java-5.0.6.jar` - 紫鸟官方SDK
- `lib/` - 依赖JAR文件目录

## ⚠️ 注意事项

1. **API密钥配置**：确保在 `application.yml` 中配置了正确的API密钥
2. **网络连接**：确保能访问 `https://sbappstoreapi.ziniao.com`
3. **图片URL**：使用真实可访问的图片URL进行测试
4. **端口占用**：确保8080端口未被占用

## 🐛 故障排除

### 如果启动失败
1. 检查Java版本：`java -version`
2. 检查依赖文件：`ls -la lib/`
3. 重新下载依赖：`./download-deps.sh`

### 如果API调用失败
1. 检查应用是否启动：`curl http://localhost:8080/api/token/health`
2. 检查API密钥配置
3. 查看日志文件：`tail -f logs/ziniao-ai-demo.log`

## 🎉 成功标志

如果看到以下信息，说明一切正常：

```
=== 紫鸟AI穿衣Demo应用启动成功 ===
API文档地址: http://localhost:8080/doc.html
Swagger UI: http://localhost:8080/swagger-ui/
```

然后API测试返回：
```json
{"code":200,"message":"success","data":{"appAuthToken":"..."}}
```

恭喜！您的紫鸟AI穿衣API集成已经成功运行！🎊
