#!/bin/bash

# 调试字段映射功能的测试脚本
# 用于查看详细的调试日志

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_URL="http://localhost:8080"
ENDPOINT="/api/feishu/bitable/images/field-mapping-upload"

# 测试数据配置
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  字段映射功能调试测试${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查服务器状态
echo -e "${YELLOW}1. 检查服务器状态...${NC}"
if curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器未启动或无法访问${NC}"
    echo "请确保服务器在 $SERVER_URL 上运行"
    exit 1
fi

echo ""

# 发送测试请求
echo -e "${YELLOW}2. 发送字段映射测试请求...${NC}"
echo "接口: POST $SERVER_URL$ENDPOINT"
echo ""

echo "请求数据:"
cat << EOF
{
  "appToken": "$APP_TOKEN",
  "tableId": "$TABLE_ID",
  "viewId": "$VIEW_ID",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "pageSize": 1,
  "updateBitableWithLocalUrl": true,
  "includeImageDetails": true
}
EOF

echo ""
echo "发送请求..."

RESPONSE=$(curl -s -X POST "$SERVER_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "pageSize": 1,
    "updateBitableWithLocalUrl": true,
    "includeImageDetails": true
  }')

echo ""
echo -e "${YELLOW}3. 响应结果:${NC}"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"

echo ""
echo -e "${YELLOW}4. 查看最新的服务器日志...${NC}"
echo "最近的日志内容:"
echo "----------------------------------------"

# 查看最新的日志文件
if [ -f "logs/ziniao-api.log" ]; then
    tail -50 logs/ziniao-api.log | grep -E "(字段映射|processFieldMappingRecordImages|检查字段|处理字段|图片字段|isImageField)" --color=always
elif [ -f "logs/application.log" ]; then
    tail -50 logs/application.log | grep -E "(字段映射|processFieldMappingRecordImages|检查字段|处理字段|图片字段|isImageField)" --color=always
else
    echo "未找到日志文件"
fi

echo ""
echo "----------------------------------------"

# 分析结果
echo ""
echo -e "${BLUE}📋 调试分析${NC}"

if echo "$RESPONSE" | grep -q '"success":true'; then
    TOTAL_IMAGES=$(echo "$RESPONSE" | grep -o '"totalImages":[0-9]*' | cut -d':' -f2)
    SUCCESS_COUNT=$(echo "$RESPONSE" | grep -o '"successfulDownloads":[0-9]*' | cut -d':' -f2)
    
    if [ "$TOTAL_IMAGES" = "0" ]; then
        echo -e "${YELLOW}⚠️ 请求成功但没有找到图片${NC}"
        echo "可能的原因:"
        echo "1. 字段名称不匹配"
        echo "2. 字段值不是图片格式"
        echo "3. 字段为空"
        echo "4. isImageField 判断逻辑有问题"
        echo ""
        echo "请检查上面的日志，查看:"
        echo "- 记录包含哪些字段"
        echo "- 字段值的类型和内容"
        echo "- isImageField 的判断结果"
    else
        echo -e "${GREEN}✅ 找到了 $TOTAL_IMAGES 个图片${NC}"
        echo "成功下载: $SUCCESS_COUNT"
    fi
elif echo "$RESPONSE" | grep -q '"code":1254024'; then
    echo -e "${RED}❌ 仍然存在 InvalidFieldNames 错误${NC}"
    echo "修复可能没有生效，需要重新编译和部署"
else
    echo -e "${RED}❌ 请求失败${NC}"
    ERROR_MSG=$(echo "$RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    echo "错误信息: $ERROR_MSG"
fi

echo ""
echo "调试测试完成！"
