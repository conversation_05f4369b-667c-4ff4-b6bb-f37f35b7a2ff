#!/bin/bash

# 字段映射图片上传优化功能测试脚本
# 演示如何使用优化后的字段映射功能，包括获取行信息和指定行处理

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
BASE_URL="http://localhost:8080"
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo -e "${BLUE}=== 字段映射图片上传优化功能测试 ===${NC}"
echo ""

# 步骤1：获取表格记录信息
echo -e "${YELLOW}步骤1: 获取表格记录信息，了解行索引和记录ID${NC}"
echo "请求URL: $BASE_URL/api/feishu/bitable/records/info"

RECORDS_INFO_RESPONSE=$(curl -s -X POST "$BASE_URL/api/feishu/bitable/records/info" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"pageSize\": 5
  }")

echo ""
echo -e "${GREEN}表格记录信息响应:${NC}"
echo "$RECORDS_INFO_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RECORDS_INFO_RESPONSE"

# 从响应中提取第一条记录的信息
FIRST_RECORD_ID=$(echo "$RECORDS_INFO_RESPONSE" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('records'):
        print(data['records'][0]['recordId'])
    else:
        print('')
except:
    print('')
" 2>/dev/null)

echo ""
echo -e "${BLUE}从响应中提取的第一条记录ID: $FIRST_RECORD_ID${NC}"

echo ""
echo -e "${YELLOW}步骤2: 使用字段映射功能处理所有行（默认行为）${NC}"

curl -s -X POST "$BASE_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"fieldMapping\": {
      \"👚上装正面图\": \"👚上装正面图url\",
      \"👚上装背面图\": \"👚上装背面图url\",
      \"使用垫图粘贴\": \"使用垫图粘贴url\"
    },
    \"updateBitableWithLocalUrl\": true,
    \"pageSize\": 3
  }" | python3 -m json.tool 2>/dev/null

echo ""
echo -e "${YELLOW}步骤3: 使用行索引处理第1行数据${NC}"

curl -s -X POST "$BASE_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"targetRowIndex\": 1,
    \"fieldMapping\": {
      \"👚上装正面图\": \"👚上装正面图url\",
      \"使用垫图粘贴\": \"使用垫图粘贴url\"
    },
    \"updateBitableWithLocalUrl\": true
  }" | python3 -m json.tool 2>/dev/null

echo ""
echo -e "${YELLOW}步骤4: 使用记录ID处理特定记录${NC}"

if [ ! -z "$FIRST_RECORD_ID" ]; then
    echo "使用记录ID: $FIRST_RECORD_ID"
    
    curl -s -X POST "$BASE_URL/api/feishu/bitable/images/field-mapping-upload" \
      -H "Content-Type: application/json" \
      -d "{
        \"appToken\": \"$APP_TOKEN\",
        \"tableId\": \"$TABLE_ID\",
        \"viewId\": \"$VIEW_ID\",
        \"targetRecordId\": \"$FIRST_RECORD_ID\",
        \"fieldMapping\": {
          \"👚上装背面图\": \"👚上装背面图url\",
          \"使用垫图粘贴\": \"使用垫图粘贴url\"
        },
        \"updateBitableWithLocalUrl\": true
      }" | python3 -m json.tool 2>/dev/null
else
    echo -e "${RED}未能获取到记录ID，跳过此步骤${NC}"
fi

echo ""
echo -e "${YELLOW}步骤5: 演示不同字段名称映射${NC}"

curl -s -X POST "$BASE_URL/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"targetRowIndex\": 2,
    \"fieldMapping\": {
      \"👚上装正面图\": \"前面图片链接\",
      \"👚上装背面图\": \"后面图片链接\",
      \"使用垫图粘贴\": \"垫图链接\"
    },
    \"updateBitableWithLocalUrl\": true
  }" | python3 -m json.tool 2>/dev/null

echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo ""
echo -e "${BLUE}功能说明:${NC}"
echo "1. 获取记录信息接口: /api/feishu/bitable/records/info"
echo "   - 返回表格中所有记录的行索引和记录ID"
echo "   - 帮助用户确定要处理的具体行"
echo ""
echo "2. 字段映射图片上传接口: /api/feishu/bitable/images/field-mapping-upload"
echo "   - fieldMapping: 支持不同的字段名称映射"
echo "   - targetRowIndex: 指定要处理的行索引（从1开始）"
echo "   - targetRecordId: 指定要处理的记录ID（优先级高于targetRowIndex）"
echo "   - 不指定任何目标参数时，处理所有行"
echo ""
echo -e "${YELLOW}如何获取行参数:${NC}"
echo "1. 在飞书表格中查看行号（从1开始计数）"
echo "2. 使用 /api/feishu/bitable/records/info 接口获取记录信息"
echo "3. 从返回的 records 数组中获取 rowIndex 或 recordId"
echo ""
echo -e "${GREEN}优化特性:${NC}"
echo "✅ 支持不同的字段名称映射（前面字段获取图片，后面字段回写URL）"
echo "✅ 可以指定处理特定行（通过行索引或记录ID）"
echo "✅ 提供记录信息查询接口，方便获取行参数"
echo "✅ 优先级：targetRecordId > targetRowIndex > 处理所有行"
