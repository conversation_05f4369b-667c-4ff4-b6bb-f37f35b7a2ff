# 验证代理URL回写功能测试

## 🎯 测试目标

验证 `/api/feishu/bitable/specific-image-fields` 接口在回写到飞书多维表格时，是否使用的是代理URL而不是直接的静态文件URL。

## 🔍 当前实现分析

### 调用链分析

1. **接口调用**: `POST /api/feishu/bitable/specific-image-fields`
2. **处理方法**: `FeishuBitableService.processSpecificImageFields()`
3. **字段处理**: `processUrlStringField()` 或 `processImageField()`
4. **图片下载**: `downloadImageFromUrl(imageUrl, fileName)`
5. **保存图片**: `saveImageFromConnection(connection, originalName)`
6. **URL生成**: `generateImageUrl(relativePath)` ✅ **这里应该返回代理URL**
7. **设置URL**: `imageInfo.setLocalAccessUrl(localUrl)`
8. **回写数据**: `updatedFields.put(fieldName, imageInfo.getLocalAccessUrl())`

### 关键代码位置

#### 1. URL生成逻辑 (第1015行)
```java
// 生成完整的访问URL
String fileUrl = generateImageUrl(relativePath);
```

#### 2. generateImageUrl方法 (第1430行)
```java
private String generateImageUrl(String relativePath) {
    // 如果启用了图片代理且服务可用，使用代理URL
    if (imageProxyEnabled && imageProxyService != null && isImageFile(relativePath)) {
        logger.debug("使用图片代理URL: {}", relativePath);
        String proxyUrl = generateSimpleProxyUrl(relativePath);
        if (proxyUrl != null) {
            return proxyUrl;  // 返回代理URL
        }
        logger.warn("生成代理URL失败，回退到直接URL: {}", relativePath);
    }
    // 使用传统的直接URL方式
    return serverBaseUrl + relativePath;
}
```

#### 3. 回写逻辑 (第1292行和1297行)
```java
// 对于URL字符串字段，直接回写本地URL
if (fieldValue instanceof String) {
    updatedFields.put(fieldName, imageInfo.getLocalAccessUrl());
    logger.info("准备回写URL字段 {}: {} -> {}", fieldName, fieldValue, imageInfo.getLocalAccessUrl());
} else {
    // 对于附件字段，将本地URL写回到对应的URL字段
    String targetFieldName = fieldName.replace("附件", "");
    updatedFields.put(targetFieldName, imageInfo.getLocalAccessUrl());
    logger.info("准备回写URL字段 {} (从附件字段 {}): {}", targetFieldName, fieldName, imageInfo.getLocalAccessUrl());
}
```

## ✅ 理论验证

根据代码分析，当前实现**应该已经**在回写代理URL，因为：

1. `downloadImageFromUrl()` → `saveImageFromConnection()` → `generateImageUrl()`
2. `generateImageUrl()` 会检查 `imageProxyEnabled` 配置
3. 如果启用代理，会调用 `generateSimpleProxyUrl()` 返回代理URL
4. 这个代理URL被设置到 `imageInfo.setLocalAccessUrl()`
5. 回写时使用 `imageInfo.getLocalAccessUrl()`

## 🧪 实际测试步骤

### 步骤1: 确认配置启用
```yaml
# 检查 application-prod.yml
image:
  proxy:
    enabled: true  # 确保这个为true
```

### 步骤2: 测试接口调用
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "recordId": "your_record_id",
    "updateBitableWithLocalUrl": true
  }' \
  "http://39.108.93.224:18088/api/feishu/bitable/specific-image-fields"
```

### 步骤3: 检查响应中的URL格式
期望的响应格式：
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "recordId": "recXXX",
        "imageFields": {
          "downImageUrl": [
            {
              "originalName": "image.png",
              "localAccessUrl": "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6",
              "downloadStatus": "SUCCESS"
            }
          ]
        }
      }
    ]
  }
}
```

### 步骤4: 检查飞书表格中的实际回写内容
登录飞书，查看对应的多维表格记录，确认字段中写入的URL格式：
- ✅ 期望: `http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6`
- ❌ 不期望: `http://39.108.93.224:18088/uploads/2025/07/25/image.png`

### 步骤5: 验证代理URL可访问性
```bash
# 使用回写的代理URL访问图片
curl "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6" \
  -o test-image.png

# 检查下载的图片是否正确
file test-image.png
```

## 🔧 如果发现问题的解决方案

### 问题1: 回写的仍然是直接URL

**可能原因**:
- `image.proxy.enabled` 配置为 `false`
- `ImageProxyService` 没有正确注入
- `generateSimpleProxyUrl()` 返回了 `null`

**解决方案**:
1. 检查配置文件
2. 检查应用启动日志
3. 添加更多调试日志

### 问题2: 代理URL无法访问

**可能原因**:
- ID映射没有正确建立
- `ImageProxyController` 没有正确注册
- 文件路径解析错误

**解决方案**:
1. 检查ID映射缓存
2. 验证控制器注册
3. 添加路径解析日志

## 📋 测试检查清单

### 配置检查
- [ ] `image.proxy.enabled=true`
- [ ] `ImageProxyService` 正常启动
- [ ] `ImageProxyController` 正常注册

### 功能检查
- [ ] 接口调用成功
- [ ] 图片下载成功
- [ ] 响应中包含代理URL
- [ ] 飞书表格中回写代理URL
- [ ] 代理URL可正常访问

### 日志检查
- [ ] 看到 "使用图片代理URL" 日志
- [ ] 看到 "注册图片ID映射" 日志
- [ ] 看到 "准备回写URL字段" 日志
- [ ] 没有 "生成代理URL失败" 错误

## 🎯 预期结果

如果一切正常，应该看到：

1. **接口响应**中的 `localAccessUrl` 是代理格式
2. **飞书表格**中回写的URL是代理格式
3. **代理URL**可以正常访问并返回正确的图片
4. **日志**中显示使用了代理URL生成逻辑

## 🚨 注意事项

1. **缓存问题**: 如果之前测试过，可能需要清理缓存或重启应用
2. **权限问题**: 确保飞书应用有写入表格的权限
3. **网络问题**: 确保服务器可以访问飞书API和图片URL
4. **配置热更新**: 修改配置后需要重启应用

## 📊 测试报告模板

```
测试时间: ____
测试环境: ____

配置检查:
- image.proxy.enabled: ____
- ImageProxyService状态: ____
- ImageProxyController状态: ____

接口测试:
- 调用状态: ✅/❌
- 响应URL格式: ____
- 图片下载状态: ✅/❌

回写验证:
- 飞书表格URL格式: ____
- 代理URL可访问性: ✅/❌

问题记录:
1. ____
2. ____

结论:
代理URL回写功能 ✅正常 / ❌异常
```

## 🎉 总结

根据代码分析，当前实现**理论上应该已经支持代理URL回写**。关键是要确保：

1. ✅ 配置正确启用
2. ✅ 服务正常注入
3. ✅ 调用链完整

通过上述测试步骤可以验证实际效果是否符合预期。
