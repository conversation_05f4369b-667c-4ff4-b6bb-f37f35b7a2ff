# 服务器部署配置指南

## 🚨 重要：修复图片URL显示本地地址问题

如果你在服务器上部署后，发现图片下载接口返回的URL还是显示 `http://localhost:8080`，请按照以下步骤修复：

## 📋 问题现象

在API响应中看到类似这样的URL：
```json
{
  "originalUrl": "http://localhost:8080/uploads/2025/07/25/20250725_022100_c31b135f.png",
  "tmpDownloadUrl": "http://localhost:8080/uploads/2025/07/25/20250725_022100_c31b135f.png"
}
```

但实际应该显示你的服务器地址，例如：
```json
{
  "originalUrl": "http://*************:18088/uploads/2025/07/25/20250725_022100_c31b135f.png",
  "tmpDownloadUrl": "http://*************:18088/uploads/2025/07/25/20250725_022100_c31b135f.png"
}
```

## 🔧 解决方案

### 方法一：修改 .env 文件（推荐）

1. **编辑 .env 文件**：
   ```bash
   nano .env
   ```

2. **找到 SERVER_BASE_URL 配置**：
   ```env
   # 服务器基础URL（重要：部署时必须修改为实际服务器地址）
   # 本地开发: http://localhost:8080
   # 服务器部署: http://你的服务器IP:端口
   # 例如: http://*************:18088
   SERVER_BASE_URL=http://localhost:8080
   ```

3. **修改为你的实际服务器地址**：
   ```env
   SERVER_BASE_URL=http://*************:18088
   ```
   
   或者如果你有域名：
   ```env
   SERVER_BASE_URL=https://yourdomain.com
   ```

4. **保存文件并重启应用**：
   ```bash
   # 如果使用Docker
   docker stop ziniao-ai-demo-container
   ./docker-run.sh
   
   # 如果直接运行JAR
   ./start.sh
   ```

### 方法二：设置环境变量

直接在启动时设置环境变量：

```bash
# Docker方式
docker run -e SERVER_BASE_URL=http://*************:18088 ...

# 直接运行方式
export SERVER_BASE_URL=http://*************:18088
java -jar target/ziniao-ai-demo-1.0.0.jar
```

### 方法三：修改 application.yml（不推荐）

如果前两种方法不可行，可以直接修改配置文件：

```bash
nano src/main/resources/application.yml
```

找到文件上传配置部分，修改：
```yaml
file:
  upload:
    server-base-url: http://*************:18088  # 改为你的服务器地址
```

然后重新编译和部署：
```bash
mvn clean package
./start.sh
```

## 🧪 验证修复效果

1. **重启应用后，测试图片下载接口**：
   ```bash
   curl -X POST "http://*************:18088/api/feishu/bitable/images/upload-to-local" \
     -H "Content-Type: application/json" \
     -d '{
       "appToken": "你的appToken",
       "tableId": "你的tableId",
       "viewId": "你的viewId"
     }'
   ```

2. **检查响应中的URL是否正确**：
   响应应该包含正确的服务器地址：
   ```json
   {
     "code": 200,
     "message": "处理成功",
     "data": {
       "records": [
         {
           "imageFields": {
             "upperOriginUrl": "http://*************:18088/uploads/2025/07/25/xxx.png"
           }
         }
       ]
     }
   }
   ```

## 📝 常见配置示例

### 本地开发
```env
SERVER_BASE_URL=http://localhost:8080
```

### 服务器部署（IP + 端口）
```env
SERVER_BASE_URL=http://*************:18088
```

### 域名部署（HTTP）
```env
SERVER_BASE_URL=http://api.yourdomain.com
```

### 域名部署（HTTPS）
```env
SERVER_BASE_URL=https://api.yourdomain.com
```

### 使用非标准端口
```env
SERVER_BASE_URL=http://yourdomain.com:8080
```

## ⚠️ 注意事项

1. **确保端口正确**：URL中的端口必须与实际服务运行的端口一致
2. **协议匹配**：如果使用HTTPS，确保配置为 `https://`
3. **防火墙设置**：确保服务器防火墙允许相应端口的访问
4. **重启生效**：修改配置后必须重启应用才能生效

## 🔍 故障排除

如果修改后仍然显示localhost，请检查：

1. **环境变量是否生效**：
   ```bash
   # 进入容器检查
   docker exec -it ziniao-ai-demo-container env | grep SERVER_BASE_URL
   ```

2. **配置文件是否正确**：
   ```bash
   # 检查应用日志
   docker logs ziniao-ai-demo-container | grep "server-base-url"
   ```

3. **重新构建镜像**（如果使用Docker）：
   ```bash
   ./docker-build.sh
   ./docker-run.sh
   ```

修复完成后，所有图片下载接口返回的URL都会使用正确的服务器地址！
