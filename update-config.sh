#!/bin/bash

# 更新JAR包中的配置文件脚本
# 用于将新的application.yml配置更新到现有的JAR包中

echo "=== 更新JAR包配置文件 ==="
echo ""

JAR_FILE="target/ziniao-ai-demo-1.0.0.jar"
BACKUP_JAR="target/ziniao-ai-demo-1.0.0.jar.backup"
TEMP_DIR="temp-update"

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "❌ JAR文件不存在: $JAR_FILE"
    exit 1
fi

echo "📦 找到JAR文件: $JAR_FILE"

# 创建备份
echo "💾 创建备份..."
cp "$JAR_FILE" "$BACKUP_JAR"
echo "✅ 备份已创建: $BACKUP_JAR"

# 创建临时目录
echo "📁 创建临时目录..."
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# 解压JAR文件
echo "📦 解压JAR文件..."
cd "$TEMP_DIR"
jar -xf "../$JAR_FILE"

# 检查是否成功解压
if [ ! -d "BOOT-INF" ]; then
    echo "❌ JAR文件解压失败"
    cd ..
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo "✅ JAR文件解压成功"

# 更新配置文件
echo "🔧 更新配置文件..."
if [ -f "../src/main/resources/application.yml" ]; then
    cp "../src/main/resources/application.yml" "BOOT-INF/classes/"
    echo "✅ application.yml 已更新"
else
    echo "❌ 源配置文件不存在: src/main/resources/application.yml"
    cd ..
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 重新打包JAR文件
echo "📦 重新打包JAR文件..."
jar -cfm "../$JAR_FILE" META-INF/MANIFEST.MF .

# 检查打包是否成功
if [ $? -eq 0 ]; then
    echo "✅ JAR文件重新打包成功"
else
    echo "❌ JAR文件重新打包失败"
    cd ..
    # 恢复备份
    cp "$BACKUP_JAR" "$JAR_FILE"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 清理临时目录
cd ..
rm -rf "$TEMP_DIR"

echo ""
echo "🎉 配置更新完成！"
echo ""
echo "📋 更新内容:"
echo "- 飞书应用配置已更新为你的实际配置"
echo "- App ID: cli_a8fe3e73bd78d00d"
echo "- App Secret: whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
echo ""
echo "🚀 现在可以启动应用:"
echo "java -jar $JAR_FILE"
echo ""
echo "📝 注意:"
echo "- 备份文件: $BACKUP_JAR"
echo "- 如果有问题，可以恢复备份: cp $BACKUP_JAR $JAR_FILE"
