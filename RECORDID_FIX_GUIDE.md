# 🔧 RecordId InvalidPageToken 错误修复指南

## 问题分析

从你提供的日志可以看出，问题的根本原因是：

### 错误日志分析
```
2025-07-25 00:35:19.926 [http-nio-8080-exec-7] INFO  c.z.c.FeishuBitableController - 收到飞书图片上传到本地请求: FeishuImageDownloadRequest{
    appToken='MgDxby4r7avigssLQnVcIQzJnm1', 
    tableId='tbl4sH8PYHUk36K0', 
    recordId='recqwIwhc6',           // ← 指定了recordId
    pageToken='recqwIwhc6',          // ← 错误：recordId被设置为pageToken
    ...
}

2025-07-25 00:35:20.594 [http-nio-8080-exec-7] DEBUG c.z.service.FeishuBitableService - 飞书API响应内容: {
    "code":1254030,
    "msg":"InvalidPageToken"         // ← 飞书API返回InvalidPageToken错误
}

2025-07-25 00:35:20.595 [http-nio-8080-exec-7] WARN  c.z.service.FeishuBitableService - 检测到token过期，抛出异常以触发重试
// ← 错误：InvalidPageToken被误判为token过期错误
```

### 问题根源

1. **参数混淆**：`recordId` 被错误地设置为 `pageToken`
2. **错误检测失误**：`InvalidPageToken` 错误被误判为token过期错误

## 修复方案

### 1. 修复错误检测逻辑

**问题**：`isTokenExpiredError` 方法将所有包含 "token" 的错误都识别为token过期错误。

**修复前**：
```java
private boolean isTokenExpiredError(String errorMessage) {
    String lowerMsg = errorMessage.toLowerCase();
    return lowerMsg.contains("token") && (
            lowerMsg.contains("expired") ||
            lowerMsg.contains("invalid") ||
            lowerMsg.contains("unauthorized") ||
            // ...
    );
}
```

**修复后**：
```java
private boolean isTokenExpiredError(String errorMessage) {
    if (errorMessage == null) {
        return false;
    }

    String lowerMsg = errorMessage.toLowerCase();
    
    // 排除非token相关的错误
    if (lowerMsg.contains("invalidpagetoken") || lowerMsg.contains("1254030")) {
        return false; // InvalidPageToken 不是token过期错误
    }
    
    // 检查具体的token相关错误码
    return lowerMsg.contains("99991661") || // 飞书缺少token错误码
           lowerMsg.contains("99991663") || // 飞书token过期错误码
           lowerMsg.contains("99991664") || // 飞书token无效错误码
           (lowerMsg.contains("token") && (
               lowerMsg.contains("expired") ||
               lowerMsg.contains("unauthorized") ||
               lowerMsg.contains("missing access token")
           ));
}
```

### 2. 修复 recordId 参数处理

**问题**：`buildBitableRequest` 方法错误地将 `recordId` 设置为 `pageToken`。

**修复前**：
```java
private FeishuBitableRequest buildBitableRequest(FeishuImageDownloadRequest request) {
    FeishuBitableRequest bitableRequest = new FeishuBitableRequest();
    // ...
    bitableRequest.setPageToken(request.getPageToken()); // 这里会包含recordId
    // ...
}
```

**修复后**：
```java
private FeishuBitableRequest buildBitableRequest(FeishuImageDownloadRequest request) {
    FeishuBitableRequest bitableRequest = new FeishuBitableRequest();
    bitableRequest.setAppToken(request.getAppToken());
    bitableRequest.setTableId(request.getTableId());
    bitableRequest.setViewId(request.getViewId());
    
    // 正确处理 pageToken - 不要将 recordId 设置为 pageToken
    bitableRequest.setPageToken(request.getPageToken());
    
    bitableRequest.setPageSize(request.getPageSize());
    bitableRequest.setFieldNames(request.getImageFields());
    
    // 处理筛选条件
    String filter = request.getFilter();
    
    // 如果指定了 recordId，构建筛选条件
    if (request.getRecordId() != null && !request.getRecordId().trim().isEmpty()) {
        String recordFilter = String.format("CurrentValue.[record_id] = \"%s\"", request.getRecordId());
        
        if (filter != null && !filter.trim().isEmpty()) {
            // 如果已有筛选条件，使用 AND 连接
            filter = "(" + filter + ") AND (" + recordFilter + ")";
        } else {
            filter = recordFilter;
        }
        
        logger.info("为记录ID {} 构建筛选条件: {}", request.getRecordId(), filter);
    }
    
    bitableRequest.setFilter(filter);

    return bitableRequest;
}
```

## 修复效果

### 修复前的错误流程
```
1. 用户请求包含 recordId: "recqwIwhc6"
2. 系统错误地将 recordId 设置为 pageToken
3. 飞书API返回 InvalidPageToken 错误 (1254030)
4. 系统误判为token过期错误
5. 抛出 TokenExpiredException
6. 请求失败
```

### 修复后的正确流程
```
1. 用户请求包含 recordId: "recqwIwhc6"
2. 系统将 recordId 转换为筛选条件: CurrentValue.[record_id] = "recqwIwhc6"
3. pageToken 保持为实际的分页标记（或为空）
4. 飞书API正确处理请求
5. 返回指定记录的数据
6. 请求成功
```

## 测试验证

### 1. 运行测试脚本
```bash
# 给脚本执行权限
chmod +x test-recordid-fix.sh

# 运行测试
./test-recordid-fix.sh
```

### 2. 手动测试

**测试无recordId的请求**：
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "pageSize": 3
  }'
```

**测试有recordId的请求**：
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "recordId": "recqwIwhc6",
    "downloadToLocal": true,
    "pageSize": 3
  }'
```

## 预期结果

### 成功响应示例
```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalImages": 5,
    "successfulDownloads": 4,
    "failedDownloads": 1,
    "processedRecords": 1,
    "imageDetails": [
      {
        "recordId": "recqwIwhc6",
        "fieldName": "图片",
        "originalName": "example.jpg",
        "downloadStatus": "SUCCESS",
        "localAccessUrl": "http://localhost:8080/uploads/2025/07/25/20250725_123456_abc123.jpg",
        "downloadTime": "2025-07-25 00:35:20"
      }
    ]
  }
}
```

## 日志监控

查看修复后的日志：
```bash
tail -f logs/ziniao-api.log | grep -E "(FeishuBitableService|构建筛选条件|InvalidPageToken)"
```

**正确的日志应该显示**：
```
INFO  c.z.service.FeishuBitableService - 为记录ID recqwIwhc6 构建筛选条件: CurrentValue.[record_id] = "recqwIwhc6"
INFO  c.z.service.FeishuBitableService - 请求URL: https://open.feishu.cn/open-apis/bitable/v1/apps/MgDxby4r7avigssLQnVcIQzJnm1/tables/tbl4sH8PYHUk36K0/records?view_id=vewgI30A6c&page_size=3&filter=CurrentValue.%5Brecord_id%5D%20%3D%20%22recqwIwhc6%22&user_id_type=user_id
INFO  c.z.service.FeishuBitableService - 飞书API响应状态码: 200
```

## 总结

通过这次修复，我们解决了两个关键问题：

1. **✅ 参数处理错误**：recordId 不再被错误地设置为 pageToken
2. **✅ 错误检测误判**：InvalidPageToken 不再被误判为token过期错误

现在你可以正常使用包含 `recordId` 的请求来获取指定记录的图片了！
