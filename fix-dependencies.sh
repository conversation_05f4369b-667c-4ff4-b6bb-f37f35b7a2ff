#!/bin/bash

# 修复依赖问题的脚本

echo "=== 修复紫鸟SDK依赖问题 ==="

# 创建lib目录
mkdir -p lib

echo "下载必要的依赖..."

# OkHttp相关依赖
DEPS=(
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp/4.9.3/okhttp-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/logging-interceptor/4.9.3/logging-interceptor-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okio/okio/2.8.0/okio-2.8.0.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib/1.4.10/kotlin-stdlib-1.4.10.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib-common/1.4.10/kotlin-stdlib-common-1.4.10.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/annotations/13.0/annotations-13.0.jar"
)

for dep in "${DEPS[@]}"; do
    filename=$(basename "$dep")
    if [ ! -f "lib/$filename" ]; then
        echo "下载 $filename..."
        if command -v curl &> /dev/null; then
            curl -L -o "lib/$filename" "$dep"
        elif command -v wget &> /dev/null; then
            wget -O "lib/$filename" "$dep"
        else
            echo "错误: 需要curl或wget来下载依赖"
            exit 1
        fi
        
        if [ $? -eq 0 ]; then
            echo "✅ $filename 下载成功"
        else
            echo "❌ $filename 下载失败"
        fi
    else
        echo "✅ $filename 已存在"
    fi
done

echo ""
echo "=== 依赖下载完成 ==="
echo ""
echo "现在可以启动应用："
echo "java -cp \"target/classes:sdk-java-5.0.6.jar:lib/*\" com.ziniao.ZiniaoAiDemoSimpleApplication"
echo ""
echo "或者使用Maven（推荐）："
echo "mvn spring-boot:run"
