# API更新说明

## 🔄 根据官方文档更新

我已经根据您提供的官方文档更新了API实现，解决了 `code:40002, msg:非法的参数` 错误。

## ✅ 主要更新内容

### 1. API路径更新
**之前**: `/ai/clothing/process`  
**现在**: `/linkfox-ai/image/v2/make/fittingRoom`

### 2. 请求参数格式更新
根据官方文档，现在使用正确的参数格式：

**上衣试穿**:
```json
{
  "upperOriginUrl": "服装图片URL",
  "upperImageUrl": "",
  "downOriginUrl": "",
  "downImageUrl": "",
  "modelImageUrl": "人物图片URL",
  "modelMaskImageUrl": "",
  "outputNum": ""
}
```

**下装试穿**:
```json
{
  "downOriginUrl": "服装图片URL", 
  "downImageUrl": "",
  "upperOriginUrl": "",
  "upperImageUrl": "",
  "modelImageUrl": "人物图片URL",
  "modelMaskImageUrl": "",
  "outputNum": ""
}
```

### 3. API调用方法更新
现在使用 `executeAppToken` 方法：
```java
CommonResponse response = client.executeAppToken(request, appToken);
```

### 4. 中文接口名称
更新了所有控制器的注释为中文：
- "获取紫鸟应用授权令牌"
- "刷新紫鸟应用授权令牌" 
- "AI穿衣处理 - 上下装试穿"
- "查询AI穿衣任务状态"

### 5. 启用Swagger文档
现在可以访问中文的API文档：
- http://localhost:8080/doc.html
- http://localhost:8080/swagger-ui/

## 🧪 测试方法

### 1. 重新启动应用
```bash
./start-with-deps.sh
```

### 2. 测试API
```bash
./test-api.sh
```

### 3. 手动测试上衣试穿
```bash
curl -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://cdn.linkfox.com/ai-site/test/workbench/model-test.jpg",
    "clothingImage": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "clothingType": "上衣"
  }'
```

### 4. 手动测试下装试穿
```bash
curl -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://cdn.linkfox.com/ai-site/test/workbench/model-test.jpg",
    "clothingImage": "https://cdn.linkfox.com/ai-site/test/workbench/down-true3.jpg",
    "clothingType": "下装"
  }'
```

## 📋 支持的服装类型

现在只支持官方文档中的两种类型：
- `上衣` - 上装试穿
- `下装` - 下装试穿

## 🔍 响应格式

根据官方文档，成功响应格式：
```json
{
  "msg": "成功",
  "traceId": "请求追踪ID",
  "code": "0",
  "data": {
    "id": "任务ID"
  },
  "msgKey": "消息键"
}
```

## ⚠️ 注意事项

1. **图片URL**: 请使用可公开访问的图片URL
2. **服装类型**: 只支持"上衣"和"下装"两种类型
3. **令牌**: 系统会自动获取和使用应用令牌
4. **异步处理**: API返回任务ID，可能需要轮询查询结果

## 🎯 下一步

现在API应该可以正常工作了。如果还有问题，请：
1. 检查图片URL是否可访问
2. 确认API密钥配置正确
3. 查看应用日志获取详细错误信息
