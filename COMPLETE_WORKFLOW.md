# 🎯 完整的AI穿衣工作流程

## 📋 官方接口说明

根据官方文档，AI穿衣的完整流程包括：

### 1. 提交AI穿衣任务
- **接口**: `/linkfox-ai/image/v2/make/fittingRoom`
- **返回**: 任务ID

### 2. 查询作图结果
- **接口**: `/linkfox-ai/image/v2/make/info`
- **参数**: 任务ID
- **返回**: 任务状态和结果图片

## 🚀 完整工作流程示例

### 步骤1：提交AI穿衣任务

```bash
# 提交穿衣任务
curl -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
    "clothingImage": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "clothingType": "上衣"
  }'
```

**成功响应**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "1945877291232894976",
    "status": "submitted",
    "progress": 0
  }
}
```

### 步骤2：查询任务结果

```bash
# 使用返回的任务ID查询结果
curl -X GET "http://localhost:8080/api/clothing/result/1945877291232894976"
```

**可能的响应状态**：

#### 排队中 (status=1)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "1945877291232894976",
    "status": "queuing",
    "progress": 10
  }
}
```

#### 生成中 (status=2)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "1945877291232894976",
    "status": "processing",
    "progress": 50
  }
}
```

#### 成功完成 (status=3)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "1945877291232894976",
    "status": "completed",
    "progress": 100,
    "resultImage": "https://test-file-ai.linkfox.com/IDEOGRAM/xxx/result.jpg?Expires=xxx&OSSAccessKeyId=xxx&Signature=xxx"
  }
}
```

#### 失败 (status=4)
```json
{
  "code": 200,
  "message": "任务失败: 错误描述",
  "data": {
    "taskId": "1945877291232894976",
    "status": "failed",
    "progress": 0
  }
}
```

## 🔄 轮询查询示例

```bash
#!/bin/bash

# 提交任务并获取任务ID
RESPONSE=$(curl -s -X POST "http://localhost:8080/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d '{
    "personImage": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
    "clothingImage": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "clothingType": "上衣"
  }')

# 提取任务ID
TASK_ID=$(echo $RESPONSE | jq -r '.data.taskId')
echo "任务ID: $TASK_ID"

# 轮询查询结果
MAX_ATTEMPTS=60  # 最多查询60次
ATTEMPT=0

while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
    echo "第 $((ATTEMPT+1)) 次查询..."
    
    RESULT=$(curl -s "http://localhost:8080/api/clothing/result/$TASK_ID")
    STATUS=$(echo $RESULT | jq -r '.data.status')
    PROGRESS=$(echo $RESULT | jq -r '.data.progress')
    
    echo "状态: $STATUS, 进度: $PROGRESS%"
    
    if [ "$STATUS" = "completed" ]; then
        RESULT_IMAGE=$(echo $RESULT | jq -r '.data.resultImage')
        echo "✅ 任务完成！"
        echo "结果图片: $RESULT_IMAGE"
        break
    elif [ "$STATUS" = "failed" ]; then
        echo "❌ 任务失败"
        break
    fi
    
    # 等待5秒后继续查询
    sleep 5
    ATTEMPT=$((ATTEMPT+1))
done

if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
    echo "⏰ 查询超时"
fi
```

## 📋 API接口总览

### 令牌管理
- `GET /api/token/app` - 获取应用令牌
- `POST /api/token/app/refresh` - 刷新令牌
- `GET /api/token/health` - 健康检查

### AI穿衣
- `POST /api/clothing/process` - 提交AI穿衣任务
- `GET /api/clothing/result/{taskId}` - 查询任务结果（官方接口）
- `GET /api/clothing/task/{taskId}` - 查询任务状态（兼容接口）
- `GET /api/clothing/health` - 健康检查

## ⚠️ 重要提醒

### 1. 图片地址有效期
- 返回的结果图片地址是**临时地址**
- **有效期只有8小时**
- 请及时下载保存到您的存储服务中

### 2. 刷新图片地址
- 如果图片地址过期，可以重新调用结果查询接口
- 系统会刷新图片地址

### 3. 支持的服装类型
- `上衣` - 上装试穿
- `下装` - 下装试穿

### 4. 图片要求
- 使用公开可访问的HTTP/HTTPS URL
- 确保图片格式符合要求

## 🌐 访问文档

- **Knife4j文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui/

## 🎉 总结

现在您有了完整的AI穿衣解决方案：
1. ✅ 任务提交 - 正常工作
2. ✅ 结果查询 - 基于官方接口实现
3. ✅ 状态轮询 - 支持完整的状态流转
4. ✅ 错误处理 - 完整的异常处理机制

整个工作流程已经完全按照官方文档实现！🎊
