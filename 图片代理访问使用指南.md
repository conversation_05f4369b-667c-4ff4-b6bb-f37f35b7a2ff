# 图片代理访问使用指南

## 🎯 功能概述

新的图片代理访问功能提供了一种更安全、更灵活的图片访问方式，替代了传统的直接静态文件URL访问。

### 🔄 访问方式对比

**传统方式（直接静态URL）：**
```
http://39.108.93.224:18088/uploads/2025/07/25/20250725_025105_1543619a.png
```

**新方式（代理访问）：**
```
http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6
```

## ⚙️ 配置说明

在 `application-prod.yml` 中添加了以下配置：

```yaml
# 图片代理访问配置
image:
  proxy:
    # 是否启用图片代理访问（true=使用代理URL，false=使用直接静态URL）
    enabled: true
    # 加密密钥（用于路径加密和ID生成）
    secret-key: ziniao-image-proxy-2025-prod
    # 是否启用ID映射（true=使用短ID，false=使用加密路径）
    enable-id-mapping: true
    # 缓存配置
    cache:
      # 图片ID映射缓存大小
      max-size: 10000
      # 缓存过期时间（秒）
      expire-time: 86400
```

## 🚀 API接口

### 1. 通过图片ID访问图片
```
GET /api/image-proxy/id/{imageId}
```

**示例：**
```bash
curl "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6"
```

### 2. 通过加密路径访问图片
```
GET /api/image-proxy/view/{encodedPath}
```

**示例：**
```bash
curl "http://39.108.93.224:18088/api/image-proxy/view/L3VwbG9hZHMvMjAyNS8wNy8yNS8yMDI1MDcyNV8wMjUxMDVfMTU0MzYxOWEucG5nfDE3MzI1MjM0NTY3ODk.a1b2"
```

### 3. 获取图片信息
```
GET /api/image-proxy/info/{encodedPath}
```

**响应示例：**
```json
{
  "filename": "20250725_025105_1543619a.png",
  "size": 1024000,
  "contentType": "image/png",
  "lastModified": 1732523456789
}
```

### 4. 生成代理URL
```
POST /api/image-proxy/generate-url
```

**请求参数：**
```
filePath: /uploads/2025/07/25/20250725_025105_1543619a.png
```

**响应示例：**
```json
{
  "originalPath": "/uploads/2025/07/25/20250725_025105_1543619a.png",
  "proxyUrl": "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6"
}
```

## 🔧 自动转换

### 文件上传服务
当 `image.proxy.enabled=true` 时，`FileUploadService` 会自动为图片文件生成代理URL：

```java
// 上传图片后返回的URL
{
  "success": true,
  "data": {
    "fileUrl": "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6",
    "originalName": "image.png",
    // ... 其他字段
  }
}
```

### 飞书图片下载服务
飞书多维表格图片下载后也会自动生成代理URL：

```java
// 飞书图片下载响应
{
  "success": true,
  "data": {
    "images": [
      {
        "originalName": "feishu_image.png",
        "localAccessUrl": "http://39.108.93.224:18088/api/image-proxy/id/b2c3d4e5f6a1",
        "downloadStatus": "SUCCESS"
      }
    ]
  }
}
```

## 🛡️ 安全特性

### 1. 路径隐藏
- 真实文件路径不会暴露在URL中
- 使用短ID或加密路径访问

### 2. 访问控制
- 自动验证文件是否在允许的目录内
- 防止路径遍历攻击

### 3. 内容验证
- 验证文件类型是否为图片
- 检查文件是否存在

## 📝 使用示例

### 1. 启用代理访问
```yaml
image:
  proxy:
    enabled: true
```

### 2. 上传图片并获取代理URL
```bash
curl -X POST \
  -F "file=@image.png" \
  "http://39.108.93.224:18088/api/file/upload"
```

**响应：**
```json
{
  "success": true,
  "data": {
    "fileUrl": "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6"
  }
}
```

### 3. 访问图片
```bash
curl "http://39.108.93.224:18088/api/image-proxy/id/a1b2c3d4e5f6" \
  -o downloaded_image.png
```

### 4. 获取飞书图片并使用代理URL
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "imageFields": ["image_field"]
  }' \
  "http://39.108.93.224:18088/api/feishu/bitable/images"
```

## 🔄 迁移指南

### 从直接URL迁移到代理URL

1. **更新配置**
   ```yaml
   image:
     proxy:
       enabled: true
   ```

2. **重启应用**
   ```bash
   # 重启Java应用
   systemctl restart your-app
   ```

3. **验证功能**
   - 上传新图片，检查返回的URL格式
   - 访问代理URL，确认图片正常显示
   - 检查日志，确认没有错误

### 兼容性说明

- 启用代理访问后，新上传的图片将使用代理URL
- 已存在的直接URL仍然可以正常访问（通过静态文件映射）
- 可以通过配置随时切换回直接URL模式

## 🐛 故障排除

### 1. 图片无法访问
- 检查 `image.proxy.enabled` 配置
- 确认 `ImageProxyService` 正常启动
- 查看应用日志中的错误信息

### 2. ID映射失效
- 检查缓存配置
- 重启应用重建映射关系
- 确认密钥配置正确

### 3. 性能问题
- 调整缓存大小和过期时间
- 监控内存使用情况
- 考虑使用外部缓存（如Redis）

## 📊 监控建议

### 日志关键字
- `图片代理访问请求`
- `生成代理URL`
- `注册图片ID映射`
- `图片代理访问失败`

### 性能指标
- 代理访问响应时间
- 缓存命中率
- 错误率统计

## 🎉 总结

图片代理访问功能提供了：
- ✅ 更安全的图片访问方式
- ✅ 隐藏真实文件路径
- ✅ 灵活的访问控制
- ✅ 向后兼容性
- ✅ 简单的配置管理

通过简单的配置即可启用，无需修改现有的业务逻辑代码。
