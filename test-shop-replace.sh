#!/bin/bash

# 商品替换接口测试脚本

# 设置服务器地址
SERVER_URL="http://localhost:8080"

# 测试商品替换接口
echo "=== 测试商品替换接口 ==="

# 构建请求数据
REQUEST_DATA='{
  "imageUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/96a7a42fc965491dbc625d2db09f2478.png",
  "sourceImageUrl": "",
  "targetOriginUrl": "https://linkfoxai-ailab.oss-cn-shenzhen.aliyuncs.com/UPLOAD/1701144666432344064/2024/11/18/9778678b4724467485346248032387ec.png",
  "targetImageUrl": "",
  "denoiseStrength": "0.5",
  "imageOutputWidth": "",
  "imageOutputHeight": "",
  "outputNum": 1
}'

echo "请求数据:"
echo "$REQUEST_DATA" | jq .

echo ""
echo "发送商品替换请求..."

# 发送请求
RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/shopReplace" \
  -H "Content-Type: application/json" \
  -d "$REQUEST_DATA")

echo "响应结果:"
echo "$RESPONSE" | jq .

# 检查响应
if echo "$RESPONSE" | jq -e '.success == true' > /dev/null; then
    echo ""
    echo "✅ 商品替换请求提交成功!"
    
    # 提取任务ID
    TASK_ID=$(echo "$RESPONSE" | jq -r '.data.data.id // empty')
    if [ -n "$TASK_ID" ]; then
        echo "任务ID: $TASK_ID"
        
        echo ""
        echo "=== 查询任务结果 ==="
        echo "等待3秒后查询任务结果..."
        sleep 3
        
        # 查询任务结果
        RESULT_RESPONSE=$(curl -s -X GET "$SERVER_URL/api/clothing/result/$TASK_ID")
        echo "任务结果:"
        echo "$RESULT_RESPONSE" | jq .
    else
        echo "⚠️  未能获取任务ID"
    fi
else
    echo ""
    echo "❌ 商品替换请求失败"
    echo "错误信息: $(echo "$RESPONSE" | jq -r '.msg // .message // "未知错误"')"
fi

echo ""
echo "=== 测试完成 ==="
