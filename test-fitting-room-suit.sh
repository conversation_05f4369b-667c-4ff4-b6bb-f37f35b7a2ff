#!/bin/bash

# AI穿衣-连体衣接口测试脚本

# 设置服务器地址
SERVER_URL="http://localhost:8080"

echo "=== AI穿衣-连体衣接口测试 ==="
echo "服务器地址: $SERVER_URL"
echo ""

# 测试数据（使用官方文档中的示例数据）
REQUEST_DATA='{
  "suitOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
  "suitImageUrl": "",
  "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
  "modelMaskImageUrl": "",
  "outputNum": 1
}'

echo "请求数据:"
echo "$REQUEST_DATA" | python3 -m json.tool 2>/dev/null || echo "$REQUEST_DATA"
echo ""

echo "发送AI穿衣-连体衣请求..."

# 发送请求
RESPONSE=$(curl -s -X POST "$SERVER_URL/api/clothing/fittingRoomSuit" \
  -H "Content-Type: application/json" \
  -d "$REQUEST_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 检查响应是否成功
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ AI穿衣-连体衣请求提交成功!"
    
    # 尝试提取任务ID
    TASK_ID=$(echo "$RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    if [ -n "$TASK_ID" ]; then
        echo "任务ID: $TASK_ID"
        echo "可以使用以下命令查询任务结果:"
        echo "curl -X GET \"$SERVER_URL/api/clothing/result/$TASK_ID\""
    else
        echo "响应中包含任务ID"
    fi
else
    echo "❌ AI穿衣-连体衣请求失败"
    echo "请检查服务是否正常运行"
fi

echo ""
echo "=== 测试健康检查接口 ==="
HEALTH_RESPONSE=$(curl -s -X GET "$SERVER_URL/api/clothing/health")
echo "健康检查响应: $HEALTH_RESPONSE"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "可用的接口："
echo "- POST $SERVER_URL/api/clothing/fittingRoomSuit  - AI穿衣-连体衣"
echo "- GET  $SERVER_URL/api/clothing/result/{taskId}  - 查询任务结果"
echo "- GET  $SERVER_URL/api/clothing/health           - 健康检查"
echo ""
echo "API文档地址: $SERVER_URL/doc.html"
echo "Swagger UI: $SERVER_URL/swagger-ui/"
