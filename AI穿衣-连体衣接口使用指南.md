# AI穿衣-连体衣接口使用指南

## 接口概述

AI穿衣-连体衣接口用于将连体衣穿戴到模特身上，实现智能穿衣效果。

## 接口信息

- **接口路径**: `/api/clothing/fittingRoomSuit`
- **请求方式**: `POST`
- **Content-Type**: `application/json`

## 请求参数

| 参数名称 | 参数说明 | 是否必须 | 数据类型 | 示例值 |
|---------|---------|---------|---------|--------|
| suitOriginUrl | 连体衣原图 | 是 | string | https://example.com/suit.jpg |
| suitImageUrl | 连体衣抠图结果 | 否 | string | https://example.com/suit_mask.jpg |
| modelImageUrl | 模特姿势图 1. 图片地址 2. 调用作图素材取地址(type=6) | 是 | string | https://example.com/model.jpg |
| modelMaskImageUrl | 模特姿势图穿戴区域图 | 否 | string | https://example.com/model_mask.jpg |
| outputNum | 输出张数 取值范围：[1,4] 默认值：1 | 否 | integer | 1 |

## 请求示例

```bash
curl -X POST "http://localhost:8080/api/clothing/fittingRoomSuit" \
  -H "Content-Type: application/json" \
  -d '{
    "suitOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "suitImageUrl": "",
    "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
    "modelMaskImageUrl": "",
    "outputNum": 1
  }'
```

## 响应格式

### 成功响应

```json
{
  "request_id": "xxx",
  "code": "0",
  "msg": "成功",
  "sub_code": null,
  "sub_msg": null,
  "data": {
    "msg": "成功",
    "code": 200,
    "traceId": "xxx",
    "data": {
      "id": "1950966478618370048"
    },
    "msgKey": null
  },
  "success": true,
  "task_id": "1950966478618370048"
}
```

### 错误响应

```json
{
  "code": "400",
  "msg": "参数错误",
  "success": false
}
```

## 使用流程

1. **提交任务**: 调用AI穿衣-连体衣接口，提交处理任务
2. **获取任务ID**: 从响应中获取任务ID (`data.data.id`)
3. **查询结果**: 使用任务ID查询处理结果

## 查询任务结果

使用返回的任务ID查询处理结果：

```bash
curl -X GET "http://localhost:8080/api/clothing/result/{taskId}"
```

## 测试脚本

项目中提供了测试脚本 `test-fitting-room-suit.sh`，可以直接运行测试：

```bash
./test-fitting-room-suit.sh
```

## 注意事项

1. **图片格式**: 支持常见的图片格式（JPG、PNG、WebP等）
2. **图片大小**: 建议图片大小不超过10MB
3. **处理时间**: AI穿衣-连体衣处理需要一定时间，请耐心等待
4. **连体衣类型**: 适用于各种连体衣、套装等整体服装
5. **输出数量**: 可以指定输出1-4张图片，默认为1张

## 参数详解

### suitOriginUrl（连体衣原图）
- 要穿戴的连体衣图片URL
- 建议使用清晰、背景简洁的连体衣图

### suitImageUrl（连体衣抠图结果）
- 连体衣的抠图结果
- 可选参数，如果不提供，系统会自动处理

### modelImageUrl（模特姿势图）
- 模特姿势图URL
- 可以是自定义图片地址
- 也可以调用作图素材取地址(type=6)

### modelMaskImageUrl（模特姿势图穿戴区域图）
- 模特姿势图的穿戴区域图
- 可选参数，用于精确控制穿戴区域

### outputNum（输出张数）
- 取值范围：[1,4]
- 默认值：1
- 指定生成图片的数量

## 与其他接口的区别

| 功能 | AI穿衣 | AI穿衣-连体衣 | AI穿戴 |
|-----|-------|-------------|-------|
| 适用服装 | 单件服装 | 连体衣、套装 | 配饰、鞋帽等 |
| 主要参数 | imageUrl, targetOriginUrl | suitOriginUrl, modelImageUrl | imageUrl, targetOriginUrl |
| 特殊参数 | modelImageUrl | suitImageUrl, modelMaskImageUrl | targetMaskUrl |

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |
| 20001 | 授权权限不足 |

## 技术支持

如有问题，请查看日志文件或联系技术支持。
