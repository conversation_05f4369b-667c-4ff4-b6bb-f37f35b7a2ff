# 🔧 outputNum 参数修复说明

## 问题描述

`/api/clothing/fittingRoom` 接口中的 `outputNum` 参数传递了但未实现效果，无论传多少张都只返回一张图片。

## 问题根因

1. **ResultService 逻辑问题**: 在 `ResultService.java` 第136-143行，代码只取第一张成功的图片就 `break` 了
2. **数据结构限制**: `ClothingResponse.ClothingData` 只有一个 `resultImage` 字段，不支持多张图片

## 修复方案

### 1. 扩展数据结构

在 `ClothingResponse.ClothingData` 中添加了：
- `resultImages` 字段：存储多张图片的URL列表
- 保持 `resultImage` 字段向后兼容

```java
@JSONField(name = "result_images")
@ApiModelProperty(value = "结果图片URL列表（支持多张图片）")
private java.util.List<String> resultImages;
```

### 2. 修复处理逻辑

修改 `ResultService.java` 中的图片处理逻辑：
- 收集所有成功的图片（status="1"）
- 设置到 `resultImages` 列表中
- 自动设置第一张图片到 `resultImage` 字段（向后兼容）

```java
// 收集所有成功的图片
java.util.List<String> successfulImages = new java.util.ArrayList<>();
for (Map<String, Object> result : resultList) {
    Object resultStatus = result.get("status");
    if (resultStatus != null && "1".equals(resultStatus.toString())) {
        Object url = result.get("url");
        if (url != null) {
            successfulImages.add(url.toString());
        }
    }
}

// 设置多张图片列表
if (!successfulImages.isEmpty()) {
    clothingData.setResultImages(successfulImages);
    logger.info("成功解析到 {} 张结果图片", successfulImages.size());
}
```

## 使用方法

### 1. 提交任务

```bash
curl -X POST "http://localhost:18088/api/clothing/fittingRoom" \
  -H "Content-Type: application/json" \
  -d '{
    "upperOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
    "outputNum": 3
  }'
```

### 2. 查询结果

```bash
curl "http://localhost:18088/api/clothing/result/{taskId}"
```

### 3. 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "1234567890",
    "status": "completed",
    "progress": 100,
    "resultImage": "https://example.com/image1.jpg",
    "resultImages": [
      "https://example.com/image1.jpg",
      "https://example.com/image2.jpg",
      "https://example.com/image3.jpg"
    ]
  }
}
```

## 向后兼容性

- ✅ `resultImage` 字段保持不变，包含第一张图片
- ✅ 现有客户端代码无需修改
- ✅ 新客户端可以使用 `resultImages` 字段获取所有图片

## 测试方法

运行测试脚本验证修复效果：

```bash
./sh/test-outputnum-fix.sh
```

测试脚本会：
1. 提交 `outputNum=1` 的任务
2. 提交 `outputNum=3` 的任务
3. 等待任务完成
4. 查询结果并验证图片数量

## 修改的文件

1. `src/main/java/com/ziniao/model/ClothingResponse.java`
   - 添加 `resultImages` 字段
   - 添加相应的 getter/setter 方法
   - 修改 toString 方法

2. `src/main/java/com/ziniao/service/ResultService.java`
   - 修改图片解析逻辑
   - 收集所有成功的图片而不是只取第一张

3. `sh/test-outputnum-fix.sh` (新增)
   - 测试脚本验证修复效果

## 注意事项

1. **任务处理时间**: AI穿衣任务需要一定时间处理，建议等待30秒以上再查询结果
2. **图片有效期**: 返回的图片URL有8小时有效期，请及时下载保存
3. **outputNum 范围**: 官方文档显示取值范围为 [1,4]
4. **错误处理**: 如果某些图片生成失败，只返回成功的图片

## 验证步骤

1. 启动服务器
2. 运行测试脚本: `./sh/test-outputnum-fix.sh`
3. 检查输出，确认多张图片正确返回
4. 验证向后兼容性正常工作

修复完成后，`outputNum` 参数将正确生效，返回对应数量的图片。
