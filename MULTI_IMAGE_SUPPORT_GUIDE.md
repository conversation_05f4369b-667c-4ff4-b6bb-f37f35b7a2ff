# 🖼️ 多图片支持功能指南

## 📋 功能概述

优化后的 `/api/feishu/bitable/images/field-mapping-by-record` 接口现在完全支持单元格内多张图片的处理，能够：

1. **智能识别多图片**：自动检测单元格中的多张图片
2. **批量下载处理**：并发下载所有图片到本地
3. **智能URL回写**：根据图片数量选择最佳的回写格式
4. **可点击链接**：生成支持点击跳转的HTML链接格式

## 🎯 核心优化点

### 1. 多图片处理逻辑

**之前的问题**：
- 只处理第一张图片，忽略其他图片
- 单一URL回写，无法区分多张图片

**优化后的解决方案**：
- 处理所有图片，收集所有成功下载的URL
- 根据图片数量智能选择回写格式

### 2. URL回写格式

#### 单张图片
```
直接回写URL字符串：
"https://your-domain.com/api/image-proxy/id/12345"
```

#### 多张图片
```
HTML链接格式，支持点击跳转：
"<a href=\"url1\" target=\"_blank\">图片1</a> | <a href=\"url2\" target=\"_blank\">图片2</a>"
```

### 3. 图片命名策略

- **有原始文件名**：使用去除扩展名的文件名
- **无原始文件名**：使用 "图片1"、"图片2" 等默认名称
- **链接分隔符**：使用 ` | ` 分隔多个链接

## 📝 使用示例

### 示例1: 处理多图片字段

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-by-record" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "recordIds": ["rec123456"],
    "fieldMapping": {
      "产品图片": "产品图片链接",
      "详情图": "详情图链接"
    },
    "updateBitableWithLocalUrl": true
  }'
```

### 示例2: 批量处理多个记录

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-by-record" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_app_token",
    "tableId": "your_table_id",
    "recordIds": ["rec123456", "rec789012", "rec345678"],
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url",
      "使用垫图粘贴": "使用垫图粘贴url"
    },
    "updateBitableWithLocalUrl": true,
    "maxConcurrentDownloads": 5
  }'
```

## 🔍 处理流程详解

### 1. 图片检测阶段
```
单元格数据 → 检测图片类型 → 提取所有图片信息
```

### 2. 下载处理阶段
```
图片信息列表 → 并发下载 → 生成本地URL → 收集成功结果
```

### 3. URL构建阶段
```
成功URL列表 → 判断数量 → 选择格式 → 构建最终字符串
```

### 4. 回写阶段
```
目标字段 ← 写入URL字符串 ← 提交更新请求
```

## 📊 响应格式

### 成功响应示例

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "processId": "multi_image_test_1706789123",
    "processedRecords": 1,
    "totalImages": 3,
    "successfulDownloads": 3,
    "failedDownloads": 0,
    "records": [
      {
        "recordId": "rec123456",
        "processedFields": 2,
        "imageCount": 3,
        "successCount": 3,
        "failureCount": 0,
        "status": "SUCCESS"
      }
    ]
  }
}
```

## 🎨 飞书表格中的显示效果

### 单张图片字段
```
字段值：https://your-domain.com/api/image-proxy/id/12345
显示：可点击的蓝色链接
```

### 多张图片字段
```
字段值：<a href="url1" target="_blank">产品主图</a> | <a href="url2" target="_blank">产品侧面图</a>
显示：产品主图 | 产品侧面图 （两个可点击的链接）
```

## ⚙️ 配置参数

### 核心参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fieldMapping | Map<String,String> | ✅ | 字段映射关系 |
| recordIds | List<String> | ❌ | 指定处理的记录ID列表 |
| updateBitableWithLocalUrl | Boolean | ❌ | 是否回写URL，默认true |

### 性能优化参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| maxConcurrentDownloads | Integer | 3 | 最大并发下载数 |
| downloadTimeout | Integer | 30 | 下载超时时间（秒） |
| continueOnError | Boolean | true | 遇到错误是否继续 |

## 🚀 性能特点

### 1. 并发处理
- 支持多张图片并发下载
- 可配置并发数量，避免服务器压力

### 2. 错误容错
- 单张图片失败不影响其他图片
- 详细的错误信息记录

### 3. 内存优化
- 流式下载，不占用大量内存
- 及时释放资源

## 🔧 测试验证

### 运行测试脚本
```bash
./test-multi-image-support.sh
```

### 手动验证步骤

1. **准备测试数据**：在飞书表格中准备包含多张图片的单元格
2. **调用接口**：使用上述示例调用接口
3. **检查结果**：验证目标字段中的URL格式
4. **点击测试**：在飞书中点击链接，确认能正确跳转

## 📈 使用建议

### 1. 字段命名规范
```
源字段：产品图片、详情图、使用场景图
目标字段：产品图片链接、详情图链接、使用场景图链接
```

### 2. 批量处理策略
- 小批量：每次处理10-20个记录
- 大批量：启用智能分页，设置合理的并发数

### 3. 错误处理
- 启用 `continueOnError` 确保批量处理的稳定性
- 检查响应中的错误信息，针对性处理失败案例

## 🎉 总结

优化后的多图片支持功能提供了：

1. ✅ **完整的多图片处理**：不再遗漏任何图片
2. ✅ **智能的URL格式**：根据图片数量自动选择最佳格式
3. ✅ **用户友好的链接**：支持在飞书中直接点击跳转
4. ✅ **高性能的处理**：并发下载，错误容错
5. ✅ **灵活的配置**：丰富的参数支持各种场景

现在您可以放心地处理包含多张图片的单元格，每张图片都会被正确下载和回写，用户可以方便地点击查看每张图片！
