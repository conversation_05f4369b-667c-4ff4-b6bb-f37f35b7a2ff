# 🖼️ 特定图片字段处理功能指南

## 🎯 功能概述

本功能专门用于处理多维表格中的6个特定图片字段，实现图片的批量下载和本地URL回写：

### 支持的图片字段
- **downImageUrl**: 下载图片URL
- **downOriginUrl**: 下载原图URL  
- **modelImageUrl**: 模型图片URL
- **modelMaskImageUrl**: 模型遮罩图片URL
- **upperImageUrl**: 上层图片URL
- **upperOriginUrl**: 上层原图URL

## 🔧 核心功能

1. **智能字段识别** - 自动识别上述6个特定图片字段
2. **批量图片下载** - 并发下载图片到本地服务器
3. **本地URL生成** - 生成可访问的本地图片URL
4. **数据自动回写** - 将本地URL信息写回多维表格原字段

## 📋 API接口

### 接口地址
```
POST /api/feishu/bitable/specific-image-fields
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| appToken | String | ✅ | 多维表格的唯一标识符 | `MgDxby4r7avigssLQnVcIQzJnm1` |
| tableId | String | ✅ | 数据表的唯一标识符 | `tbl4sH8PYHUk36K0` |
| recordId | String | ❌ | 记录ID，指定则只处理该记录 | `recqwIwhc6` |
| viewId | String | ❌ | 视图ID，不传则使用默认视图 | `vewgI30A6c` |
| filter | String | ❌ | 筛选条件 | `CurrentValue.[状态] = "待处理"` |
| pageSize | Integer | ❌ | 分页大小，默认100，最大500 | `10` |
| pageToken | String | ❌ | 分页标记 | `recqwIwhc6` |
| downloadTimeout | Integer | ❌ | 下载超时时间（秒），默认30 | `60` |
| maxConcurrentDownloads | Integer | ❌ | 最大并发下载数，默认5 | `3` |
| updateBitableWithLocalUrl | Boolean | ❌ | 是否回写本地URL，默认true | `true` |

### 请求示例

```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "pageSize": 10,
  "downloadTimeout": 60,
  "maxConcurrentDownloads": 3,
  "updateBitableWithLocalUrl": true
}
```

### 响应格式

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalRecords": 5,
    "totalImages": 15,
    "successfulDownloads": 14,
    "failedDownloads": 1,
    "records": [
      {
        "recordId": "recqwIwhc6",
        "imageFields": {
          "downImageUrl": [
            {
              "fileToken": "boxcnxe8OIukXXXXXXXXXXXXXX",
              "originalName": "image1.jpg",
              "fileType": "image/jpeg",
              "fileSize": 1024000,
              "originalUrl": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx",
              "localAccessUrl": "http://localhost:8080/uploads/2025/07/25/20250725_123456_abc123.jpg",
              "downloadStatus": "SUCCESS",
              "downloadTime": "2025-07-25 12:34:56"
            }
          ],
          "modelImageUrl": [...]
        }
      }
    ]
  }
}
```

## 🚀 使用示例

### 示例1: 处理所有记录
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/specific-image-fields" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "pageSize": 10,
    "updateBitableWithLocalUrl": true
  }'
```

### 示例2: 处理指定记录
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/specific-image-fields" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "updateBitableWithLocalUrl": true
  }'
```

### 示例3: 使用筛选条件
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/specific-image-fields" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "filter": "CurrentValue.[状态] = \"待处理\"",
    "updateBitableWithLocalUrl": true
  }'
```

## 📊 数据回写格式

当 `updateBitableWithLocalUrl` 为 `true` 时，系统会在原有附件信息基础上添加本地访问信息：

### 原始附件格式
```json
{
  "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
  "name": "image.jpg",
  "type": "image/jpeg",
  "size": 1024000,
  "url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx"
}
```

### 回写后的格式
```json
{
  "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
  "name": "image.jpg",
  "type": "image/jpeg",
  "size": 1024000,
  "url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx",
  "local_url": "http://localhost:8080/uploads/2025/07/25/20250725_123456_abc123.jpg",
  "local_download_time": "2025-07-25 12:34:56",
  "local_status": "downloaded"
}
```

## 🔍 错误处理

### 常见错误码
- **400**: 请求参数错误
- **401**: 认证失败（99991661错误）
- **500**: 服务器内部错误

### 错误示例
```json
{
  "code": 401,
  "message": "认证失败: 99991661",
  "success": false
}
```

## 🛠️ 技术实现

### 处理流程
1. **参数验证** - 验证必填参数
2. **获取记录** - 从多维表格获取数据
3. **字段识别** - 识别6个特定图片字段
4. **并发下载** - 多线程下载图片到本地
5. **URL生成** - 生成本地访问URL
6. **数据回写** - 更新多维表格字段

### 核心类
- `FeishuSpecificImageFieldsRequest`: 请求参数模型
- `FeishuBitableService.processSpecificImageFields()`: 核心处理方法
- `FeishuBitableController.processSpecificImageFields()`: API接口

## 📝 注意事项

1. **权限要求**: 确保飞书应用有读写多维表格的权限
2. **网络连接**: 确保服务器能访问飞书API和图片URL
3. **存储空间**: 确保服务器有足够的存储空间
4. **并发控制**: 合理设置并发下载数，避免过载
5. **Token管理**: 确保使用有效的访问令牌

## 🔗 相关链接

- [飞书多维表格API文档](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/sheets-v3/spreadsheet-sheet/query)
- [Swagger API文档](http://localhost:8080/swagger-ui.html)
- [测试脚本](./test-specific-image-fields.sh)
