# =================================================================
# 紫鸟AI穿衣演示 - 简化配置文件
# =================================================================
# 
# 快速配置说明：
# 1. 将下面的 APP_ID 和 PRIVATE_KEY 替换为您的实际API凭据
# 2. 保存文件为 .env
# 3. 运行: docker-compose up -d
#
# =================================================================

# -----------------------------------------------------------------
# 🔑 必须配置 - 紫鸟API凭据
# -----------------------------------------------------------------
# 请从紫鸟开放平台获取以下信息并替换：

# 您的应用ID（必须替换）
ZINIAO_API_APP_ID=your_app_id_here

# 您的应用私钥（必须替换）
ZINIAO_API_PRIVATE_KEY=your_private_key_here

# API基础URL（通常不需要修改）
ZINIAO_API_BASE_URL=https://sbappstoreapi.ziniao.com

# -----------------------------------------------------------------
# ⚙️ 基础配置 - 根据需要调整
# -----------------------------------------------------------------

# 应用端口（如果8080被占用，可以改为8081、9090等）
SERVER_PORT=8080

# JVM内存配置（根据服务器配置调整）
# 小服务器: -Xmx256m -Xms128m
# 中等服务器: -Xmx512m -Xms256m  
# 大服务器: -Xmx1g -Xms512m
JAVA_OPTS=-Xmx512m -Xms256m -Djava.security.egd=file:/dev/./urandom

# 日志级别（INFO为推荐设置）
LOGGING_LEVEL_COM_ZINIAO=INFO
LOGGING_LEVEL_ROOT=WARN

# Spring Boot环境
SPRING_PROFILES_ACTIVE=docker

# -----------------------------------------------------------------
# 📝 配置示例
# -----------------------------------------------------------------
# 
# 正确的配置示例：
# ZINIAO_API_APP_ID=1234567890
# ZINIAO_API_PRIVATE_KEY=abcdef1234567890abcdef1234567890
# 
# 如果端口8080被占用，可以这样配置：
# SERVER_PORT=8081
#
# 如果服务器内存较小，可以这样配置：
# JAVA_OPTS=-Xmx256m -Xms128m -Djava.security.egd=file:/dev/./urandom
#
# =================================================================

# -----------------------------------------------------------------
# 🚀 配置完成后的启动命令
# -----------------------------------------------------------------
# 
# 1. 保存此文件为 .env
# 2. 运行以下命令启动服务：
#    docker-compose up -d
# 
# 3. 访问应用：
#    http://localhost:8080
# 
# 4. 查看日志：
#    docker-compose logs -f
# 
# 5. 停止服务：
#    docker-compose down
#
# =================================================================
